// knowledge:start v4 fix - separate client and server components
import { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { LayoutClientWrapper } from '@/components/layout-client-wrapper';
import { StagewiseToolbar } from '@stagewise/toolbar-next';
// Server component doesn't directly import client components that use hooks

const inter = Inter({ subsets: ['latin'] });

const stagewiseConfig = {
  plugins: []
};

export const metadata: Metadata = {
  title: 'Restaurant Manager',
  description: 'Modern restaurant management platform',
};

export const viewport: Viewport = {
  themeColor: '#ffffff',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5, // Allow some zoom for accessibility
  userScalable: true, // Enable zoom for accessibility
  viewportFit: 'cover', // Handle safe areas on mobile devices
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning lang="en" dir="ltr" className="h-full overflow-x-hidden">
      <head>
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="mobile-web-app-capable" content="yes" />
      </head>
      <body className="h-full font-tajawal text-foreground mobile-safe-area">
        <Providers>
          <LayoutClientWrapper>
            {children}
          </LayoutClientWrapper>
        </Providers>
        {process.env.NODE_ENV === 'development' && <StagewiseToolbar config={stagewiseConfig} />}
      </body>
    </html>
  );
}
// knowledge:end
