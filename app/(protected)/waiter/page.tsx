"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { EditOrderProvider } from '@/components/providers/EditOrderContext';
import EnhancedMobileOrderingInterface from '@/app/components/EnhancedMobileOrderingInterface';
import OrderList from '../../components/OrderList';
import { cn } from '@/lib/utils';

export default function WaiterPage() {
  const { isAuthenticated, loading, user } = useAuth();
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [tab, setTab] = useState('waiter');

  // Debug logging
  useEffect(() => {
    console.log('[WaiterPage] Auth state:', {
      isAuthenticated,
      loading,
      user: user ? {
        id: user.id,
        name: user.name,
        restaurantId: user.restaurantId,
        role: user.role
      } : null
    });
  }, [isAuthenticated, loading, user]);

  useEffect(() => {
    // Redirect to login if not authenticated and auth check is complete
    if (!loading && !isAuthenticated) {
      navigate('auth');
    }
  }, [isAuthenticated, loading, navigate]);

  // Show loading until auth is determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render the page when authenticated
  if (!isAuthenticated) return null;

  return (
    <EditOrderProvider>
      <div className="h-screen flex flex-col">
        <Tabs value={tab} onValueChange={setTab} className="flex-grow flex flex-col">
          {/* Minimal Header */}
          <div className="flex-shrink-0 border-b bg-background">
            <div className="px-3 py-2">
              <TabsList className="grid w-full grid-cols-2 h-9">
                <TabsTrigger value="waiter" className="text-sm">
                  Serveur
                </TabsTrigger>
                <TabsTrigger value="orders" className="text-sm">
                  Commandes
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          <TabsContent value="waiter" className="flex-grow overflow-hidden min-h-0 m-0 p-0">
            <EnhancedMobileOrderingInterface />
          </TabsContent>
          <TabsContent value="orders" className="flex-grow overflow-hidden min-h-0 m-0 p-0">
            <OrderList setTab={setTab} />
          </TabsContent>
        </Tabs>
      </div>
    </EditOrderProvider>
  );
} 