'use client';

import { AppSidebar } from "@/components/app-sidebar";
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { Menu } from "lucide-react";
import FloatingDebugButton from "@/components/debug/FloatingDebugButton";
import { AutonomousSyncProvider } from "@/components/p2p/AutonomousSyncProvider";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AutonomousSyncProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:border-0 sm:bg-transparent sm:px-6 lg:hidden safe-top">
          <SidebarTrigger className="lg:hidden touch-target">
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle Menu</span>
          </SidebarTrigger>
          <div className="flex-1 text-center">
            <h1 className="text-lg font-semibold">Restaurant Manager</h1>
          </div>
        </header>
        <main className="flex-1 flex flex-col min-h-0 safe-bottom">
          <div className="p-4 sm:p-6 flex-1 flex flex-col min-h-0">
            <ProtectedRoute>
              {children}
            </ProtectedRoute>
          </div>
        </main>
        <FloatingDebugButton />
      </SidebarInset>
    </AutonomousSyncProvider>
  );
}