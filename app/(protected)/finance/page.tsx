"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { DateRange } from 'react-day-picker';
import { format, startOfMonth, endOfMonth } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { Separator } from "@/components/ui/separator";

// Import custom components
import CompactTransactionTable, { UnifiedTransaction } from '@/app/components/finance/CompactTransactionTable';
import QuickCashActionSheet, { CashTransactionData } from '@/app/components/finance/QuickCashActionSheet';
import { formatCurrency } from '@/lib/utils/currency';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";

// Import v4 finance hooks
import { useCashRegister } from '@/lib/services/finance-service';

// Import simple caisse overview
import SimpleCaisseOverview from '@/app/components/caisse/SimpleCaisseOverview';

export default function FinancePage() {
  const { toast } = useToast();
  const { user } = useAuth();
  const { isReady: dbReady } = useUnifiedDB();

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date())
  });

  // Dialog states
  const [cashInSheetOpen, setCashInSheetOpen] = useState(false);
  const [cashOutSheetOpen, setCashOutSheetOpen] = useState(false);
  
  // Loading state for manual operations
  const [isManuallyRefreshing, setIsManuallyRefreshing] = useState(false);

  // Service hooks (v4 only)
  const {
    transactions: cashTransactions,
    addCashTransaction,
    refreshData: refreshCashData,
    loading: cashLoading,
    error: cashError
  } = useCashRegister();



  // Load data
  useEffect(() => {
    if (!dbReady || !user) return;
    
    const loadData = async () => {
      try {
        await refreshCashData();
      } catch (error) {
        console.error("Initial data loading error:", error);
        toast({
          title: "Erreur de chargement",
          description: "Impossible de charger les données de caisse. Veuillez réessayer.",
          variant: "destructive"
        });
      }
    };
    
    loadData();
  }, [dbReady, user, refreshCashData, toast]);

  // Calculate cash flow totals
  const calculateCashFlow = useCallback(() => {
    if (!cashTransactions) return { cashInAmount: 0, cashOutAmount: 0 };
    
    // Filter transactions by date range if set
    let filteredTransactions = cashTransactions;
    if (dateRange?.from) {
      const fromDate = format(dateRange.from, 'yyyy-MM-dd');
      const toDate = dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');
      filteredTransactions = cashTransactions.filter(tx => {
        if (!tx.time || typeof tx.time !== 'string') return false;
        const txDate = tx.time.split('T')[0];
        return txDate >= fromDate && txDate <= toDate;
      });
    }
    
    const cashInAmount = filteredTransactions
      .filter(tx => {
        const tType = (tx as any).transactionType ?? (tx as any).type;
        return tType === 'manual_in' || (tx.amount > 0 && tType !== 'expense');
      })
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    const cashOutAmount = filteredTransactions
      .filter(tx => {
        const tType = (tx as any).transactionType ?? (tx as any).type;
        return tType === 'expense' || tx.amount < 0;
      })
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    return { cashInAmount, cashOutAmount };
  }, [cashTransactions, dateRange]);

  // Combine and format all transactions
  const getAllTransactions = useCallback((): UnifiedTransaction[] => {
    const allTransactions: UnifiedTransaction[] = [];
    // Add cash register transactions
    if (cashTransactions) {
      cashTransactions.forEach(tx => {
        // Map expense type to manual_out for compatibility
        const tType = (tx as any).transactionType ?? (tx as any).type;
        const type = tType === 'expense' ? 'manual_out' : (tType as any);
        
        allTransactions.push({
          id: tx.id,
          type: type as UnifiedTransaction['type'],
          amount: tx.amount,
          description: tx.description,
          time: tx.time,
          performedBy: tx.performedBy,
          relatedDocId: tx.relatedDocId,
          sessionId: tx.sessionId,
          metadata: (tx as any).metadata || undefined
        });
      });
    }
    
    // Filter by date range if set
    if (dateRange?.from) {
      const fromDate = format(dateRange.from, 'yyyy-MM-dd');
      const toDate = dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');
      return allTransactions.filter(tx => {
        if (!tx.time || typeof tx.time !== 'string') return false;
        const txDate = tx.time.split('T')[0];
        return txDate >= fromDate && txDate <= toDate;
      });
    }
    // Sort by time, newest first
    return allTransactions.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
  }, [cashTransactions, dateRange]);

  // Refresh all data
  const refreshAllData = async () => {
    setIsManuallyRefreshing(true);
    try {
      await refreshCashData();
      toast({
        title: "Données actualisées",
        description: "Les données de caisse ont été actualisées avec succès."
      });
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'actualisation des données.",
        variant: "destructive"
      });
    } finally {
      setIsManuallyRefreshing(false);
    }
  };

  // Handle cash in/out
  const handleCashTransaction = async (data: CashTransactionData) => {
    try {
      if ((data.type as any) !== 'manual_in' && (data.type as any) !== 'manual_out') {
        throw new Error('Invalid cash transaction type');
      }
      await addCashTransaction(
        data.type as any,
        data.amount,
        data.description,
        user?.name || "Unknown User"
      );
      toast({
        title: data.type === 'manual_in' ? "Dépôt enregistré" : "Retrait enregistré",
        description: `Transaction de ${formatCurrency(data.amount)} enregistrée avec succès.`
      });
      await refreshCashData();
    } catch (error) {
      console.error("Error adding cash transaction:", error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement de la transaction.",
        variant: "destructive"
      });
    }
  };

  // Calculate cash flow
  const { cashInAmount, cashOutAmount } = calculateCashFlow();

  return (
    <div className="w-full px-2 py-2 max-w-full">
      {/* Error Alert */}
      {cashError && (
        <Alert variant="destructive" className="mb-2">
          <AlertTitle>Erreur de chargement</AlertTitle>
          <AlertDescription className="flex flex-col gap-1">
            <p>Impossible de charger les données de caisse. {cashError}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshAllData}
              disabled={isManuallyRefreshing}
              className="w-fit"
            >
              {isManuallyRefreshing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Réessayer
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Caisse Overview */}
      <SimpleCaisseOverview 
        onCashIn={() => setCashInSheetOpen(true)}
        onCashOut={() => setCashOutSheetOpen(true)}
        cashInAmount={cashInAmount}
        cashOutAmount={cashOutAmount}
      />

      {/* Transaction History */}
      <div className="mt-3">
        <CompactTransactionTable
          transactions={getAllTransactions()}
          onRefresh={refreshAllData}
          isLoading={cashLoading && !cashError}
          enableHourlyGrouping={true}
          isRefreshing={isManuallyRefreshing}
        />
      </div>

      {/* Bottom Sheets for Cash In/Out */}
      <QuickCashActionSheet
        open={cashInSheetOpen}
        onOpenChange={setCashInSheetOpen}
        type="in"
        onSubmit={handleCashTransaction}
      />

      <QuickCashActionSheet
        open={cashOutSheetOpen}
        onOpenChange={setCashOutSheetOpen}
        type="out"
        onSubmit={handleCashTransaction}
      />

      {/* Loading overlay - show only on initial load */}
      {cashLoading && !cashError && (!cashTransactions || cashTransactions.length === 0) && (
        <div className="fixed inset-0 bg-white/60 flex items-center justify-center z-50">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="text-lg font-semibold">Chargement des données...</span>
          </div>
        </div>
      )}
    </div>
  );
}
