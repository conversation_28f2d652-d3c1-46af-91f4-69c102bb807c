'use client';

import { useState, useEffect, Suspense } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, WifiOff, Laptop, Smartphone, Globe, UserPlus } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { usePlatform } from "@/lib/context/platform-context";
import { useStaticNavigation } from '@/lib/utils/navigation';
import { MobileFormWrapper } from '@/components/mobile/MobileFormWrapper';
import { isWebBuild, getAuthRedirectUrl } from '@/lib/utils/build-utils';

// Define local interfaces to fix type errors
interface User {
  id: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
  permissions?: any;
  metadata?: any;
}

// Create an interface that matches the expected auth return
interface AuthReturnWithOffline {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (credentials: { identifier: string; password: string; restaurantId?: string; isStaffLogin?: boolean }) => Promise<boolean>;
  logout: () => void;
  register: (userData: { name: string; email: string; password: string; phoneNumber?: string }) => Promise<boolean>;
  isAdmin: boolean;
  isOwner: boolean;
  canManageStaff: boolean;
  offlineLogin: (role?: string) => Promise<boolean>;
  isOfflineMode: boolean;
}

function AuthPageContent() {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { navigate } = useStaticNavigation();
  
  // Cast the auth return to our complete interface
  const auth = useAuth() as unknown as AuthReturnWithOffline;
  const { isAuthenticated, user, loading: authLoading, login, register, offlineLogin, isOfflineMode } = auth;
  
  const { platform, isElectron, isCapacitor, isOnline, isStatic } = usePlatform();

  // Set isClient on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle authentication state changes
  useEffect(() => {
    console.log('🔄 [Auth] State change:', { isClient, authLoading, isAuthenticated, user: !!user });
    
    if (!isClient || authLoading) {
      console.log('⏳ [Auth] Waiting for client or auth loading...');
      return;
    }

    // Check if we're in add user mode
    const mode = searchParams?.get('mode');
    const isAddUserMode = mode === 'add_user';
    
    if (isAuthenticated && user) {
      console.log('✅ [Auth] User authenticated, preparing redirect...');
      
      // If we're in add user mode, check if this is a new user session
      if (isAddUserMode) {
        // Check if we have context about adding a user
        const contextStr = sessionStorage.getItem('temp_switch_user_context');
        if (contextStr) {
          try {
            const context = JSON.parse(contextStr);
            if (context.action === 'add_user' && context.currentUserId !== user.id) {
              // This is a new user being added, clear the context and redirect
              sessionStorage.removeItem('temp_switch_user_context');
              console.log('✅ [Auth] New user added successfully, redirecting...');
              navigate('menu');
              return;
            } else if (context.currentUserId === user.id) {
              // Same user, don't redirect - let them add a different user
              console.log('🔄 [Auth] Same user in add mode, staying on auth page');
              return;
            }
          } catch (e) {
            console.error('❌ [Auth] Error parsing switch user context:', e);
          }
        }
        // If no context or error, stay on auth page for adding user
        console.log('🔄 [Auth] Add user mode, staying on auth page');
        return;
      }
      
      // Normal authentication flow
      // Add a small delay to ensure the session is fully established
      // This prevents race conditions in static/electron apps
      const timer = setTimeout(() => {
        // Get redirect URL from query params if available
        const redirectUrl = searchParams?.get('redirect');
        if (redirectUrl) {
          console.log('🔄 [Auth] Redirecting to:', redirectUrl);
          // Remove leading slash and .html extension if present for clean navigation
          const cleanRedirectUrl = redirectUrl.replace(/^\//, '').replace(/\.html$/, '');
          navigate(cleanRedirectUrl);
          return;
        }
        // Use build-aware redirect logic
        const redirectTarget = isWebBuild() ? 'landing' : 'menu';
        console.log(`🏠 [Auth] No redirect URL, redirecting to ${redirectTarget} (${isWebBuild() ? 'web' : 'native'} build)`);
        navigate(redirectTarget);
      }, 100); // Small delay to ensure session is established

      return () => clearTimeout(timer);
    } else {
      console.log('🔐 [Auth] User not authenticated, staying on auth page');
    }
  }, [isAuthenticated, user, navigate, authLoading, isClient, searchParams]);

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('🔐 Owner login form submitted');
    setError("");
    setLoading(true);

    const formData = new FormData(e.currentTarget);
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    
    console.log('📧 Form data:', { email, password: password ? '[HIDDEN]' : 'EMPTY' });
    
    try {
      console.log('🚀 Calling login function...');
      const success = await login({ 
        identifier: email,
        password 
      });

      console.log('📊 Login result:', success);
      if (!success) {
        console.log('❌ Login failed, setting error');
        setError("Invalid email or password");
      } else {
        console.log('✅ Login successful!');
      }
      // The redirect will be handled by the useEffect hook monitoring authentication state
    } catch (error) {
      console.error("❌ Authentication error:", error);
      setError(`An unexpected error occurred: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      console.log('🏁 Login attempt finished, setting loading to false');
      setLoading(false);
    }
  };

  const handleStaffLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    const formData = new FormData(e.currentTarget);
    const username = formData.get("username") as string;
    const password = formData.get("password") as string;
    
    try {
      const success = await login({ 
        identifier: username,
        password,
        isStaffLogin: true 
      });

      if (!success) {
        setError("Invalid username or password");
      }
      // The redirect will be handled by the useEffect hook monitoring authentication state
    } catch (error) {
      console.error("Authentication error:", error);
      setError(`An unexpected error occurred: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    const formData = new FormData(e.currentTarget);
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const phoneNumber = formData.get("phoneNumber") as string;
    
    try {
      // Use the token-based registration
      const success = await register({ name, email, password, phoneNumber });
      
      if (!success) {
        setError("Registration failed. Please try again.");
      }
      // The redirect will be handled by the useEffect hook monitoring authentication state
    } catch (error) {
      console.error("Authentication error:", error);
      setError(`An unexpected error occurred: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setLoading(false);
    }
  };

  const handleOfflineLogin = async (role: string) => {
    setError("");
    setLoading(true);
    
    try {
      const success = await offlineLogin(role);
      
      if (!success) {
        setError("Offline login failed. Please try again.");
      }
      // The redirect will be handled by the useEffect hook monitoring authentication state
    } catch (error) {
      console.error("Offline login error:", error);
      setError(`An unexpected error occurred: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setLoading(false);
    }
  };

  // If still loading auth state, show loading state
  if (!isClient || authLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // If already authenticated, don't render anything (let the redirect happen)
  if (isAuthenticated) {
    // Check if we're in add user mode
    const mode = searchParams?.get('mode');
    const isAddUserMode = mode === 'add_user';
    
    if (isAddUserMode) {
      // Check if we have context about adding a user
      const contextStr = sessionStorage.getItem('temp_switch_user_context');
      if (contextStr) {
        try {
          const context = JSON.parse(contextStr);
          if (context.action === 'add_user' && context.currentUserId === user?.id) {
            // Same user in add mode, don't show redirecting - show the auth form
            // Fall through to render the auth form
          } else {
            // Different user or no context, show redirecting
            return (
              <div className="flex h-screen w-full items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-4 text-lg">Redirecting...</p>
                </div>
              </div>
            );
          }
        } catch (e) {
          console.error('❌ [Auth] Error parsing switch user context:', e);
          // Fall through to render the auth form
        }
      } else {
        // No context, fall through to render the auth form
      }
    } else {
      // Normal mode, show redirecting
      return (
        <div className="flex h-screen w-full items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-lg">Redirecting...</p>
          </div>
        </div>
      );
    }
  }

  // Get platform icon
  const getPlatformIcon = () => {
    switch (platform) {
      case 'electron':
        return <Laptop className="h-4 w-4" />;
      case 'capacitor':
        return <Smartphone className="h-4 w-4" />;
      case 'browser':
        return <Globe className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="container relative min-h-screen-mobile keyboard-adjust flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0 safe-x">
      <Link
        href="/"
        className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center text-sm font-medium text-muted-foreground hover:text-primary"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to home
      </Link>
      
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <span className="mr-2 flex items-center justify-center h-8 w-8 rounded-full bg-primary text-primary-foreground">
            🍽️
          </span>
          Restaurant Manager
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              "This platform has revolutionized how we manage our restaurant operations."
            </p>
            <footer className="text-sm">Sofia Davis - Restaurant Owner</footer>
          </blockquote>
        </div>
      </div>

      <div className="lg:p-8 p-4 flex-1 flex items-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] max-w-sm">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Welcome</h1>
            <p className="text-sm text-muted-foreground">
              Access your restaurant management system
            </p>
            
            {/* Add User Mode Indicator */}
            {searchParams?.get('mode') === 'add_user' && (
              <div className="flex items-center justify-center gap-2 text-sm font-medium text-blue-600 bg-blue-50 dark:bg-blue-950/30 dark:text-blue-400 px-3 py-2 rounded-lg border border-blue-200 dark:border-blue-800">
                <UserPlus className="h-4 w-4" />
                <span>Adding New User to Device</span>
              </div>
            )}
            
            <div className="flex flex-wrap items-center justify-center gap-2 text-sm">
              {isOfflineMode && (
                <div className="flex items-center space-x-1 text-amber-500 text-sm font-medium bg-amber-100 dark:bg-amber-950/30 px-2 py-1 rounded-full">
                  <WifiOff className="h-3.5 w-3.5" />
                  <span>Offline Mode</span>
                </div>
              )}
              {isStatic && (
                <div className="flex items-center space-x-1 text-blue-500 text-sm font-medium bg-blue-100 dark:bg-blue-950/30 px-2 py-1 rounded-full">
                  <span>Static Mode</span>
                </div>
              )}
              {platform !== 'unknown' && (
                <div className="flex items-center space-x-1 text-green-500 text-sm font-medium bg-green-100 dark:bg-green-950/30 px-2 py-1 rounded-full">
                  {getPlatformIcon()}
                  <span>{platform}</span>
                </div>
              )}
            </div>
          </div>

          <MobileFormWrapper>
            <Tabs defaultValue="signin" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="signin">Owner</TabsTrigger>
                <TabsTrigger value="staff">Staff</TabsTrigger>
                <TabsTrigger value="register">Register</TabsTrigger>
                <TabsTrigger value="offline">Offline</TabsTrigger>
              </TabsList>

            {/* Owner Login Tab */}
            <TabsContent value="signin">
              <Card>
                <form onSubmit={handleLogin}>
                  <CardHeader>
                    <CardTitle>Owner Sign In</CardTitle>
                    <CardDescription>Enter your credentials to access your restaurant account</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input 
                        id="email" 
                        name="email" 
                        required 
                        placeholder="<EMAIL>" 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input id="password" name="password" type="password" required />
                    </div>
                    {error && <p className="text-sm text-red-500">{error}</p>}
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full" disabled={loading}>
                      {loading ? "Signing in..." : "Sign In"}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            {/* Staff Login Tab */}
            <TabsContent value="staff">
              <Card>
                <form onSubmit={handleStaffLogin}>
                  <CardHeader>
                    <CardTitle>Staff Sign In</CardTitle>
                    <CardDescription>Enter the credentials provided by your restaurant owner</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">Username</Label>
                      <Input 
                        id="username" 
                        name="username" 
                        required 
                        placeholder="Your staff username" 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input id="password" name="password" type="password" required />
                    </div>
                    {error && <p className="text-sm text-red-500">{error}</p>}
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full" disabled={loading}>
                      {loading ? "Signing in..." : "Sign In"}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            {/* Register Tab */}
            <TabsContent value="register">
              <Card>
                <form onSubmit={handleRegister}>
                  <CardHeader>
                    <CardTitle>Create an Account</CardTitle>
                    <CardDescription>Enter your information to create a restaurant account</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Your Name</Label>
                      <Input id="name" name="name" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" name="email" type="email" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber">Phone Number</Label>
                      <Input id="phoneNumber" name="phoneNumber" type="tel" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input id="password" name="password" type="password" required />
                    </div>
                    {error && <p className="text-sm text-red-500">{error}</p>}
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full" disabled={loading}>
                      {loading ? "Creating Account..." : "Create Account"}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            {/* Offline Login Tab */}
            <TabsContent value="offline">
              <Card>
                <CardHeader>
                  <CardTitle>Offline Access</CardTitle>
                  <CardDescription>
                    Use offline mode when you cannot connect to the server
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Choose your role to access the system offline. All data will be stored locally.
                  </p>
                  {error && <p className="text-sm text-red-500">{error}</p>}
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button
                    onClick={() => handleOfflineLogin('owner')}
                    className="w-full"
                    disabled={loading}
                    variant="default"
                  >
                    {loading ? "Signing in..." : "Owner Access"}
                  </Button>
                  <Button
                    onClick={() => handleOfflineLogin('staff')}
                    className="w-full"
                    disabled={loading}
                    variant="outline"
                  >
                    {loading ? "Signing in..." : "Staff Access"}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
          </MobileFormWrapper>
        </div>
      </div>
    </div>
  );
}

export default function AuthPage() {
  return (
    <Suspense fallback={
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    }>
      <AuthPageContent />
    </Suspense>
  );
}