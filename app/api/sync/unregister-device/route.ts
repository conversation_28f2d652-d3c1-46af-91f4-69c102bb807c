import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';

export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await req.json();
    const { deviceId } = body;

    if (!deviceId) {
      return NextResponse.json({ error: 'deviceId is required' }, { status: 400 });
    }

    const database = databaseV4.getDatabase();
    const collection = database.collection('sync_devices');

    const result = await collection.updateOne(
      { deviceId, restaurantId: decoded.restaurantId },
      { $set: { status: 'inactive', lastSeen: new Date() } }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json({ 
        error: 'Device not found or not registered' 
      }, { status: 404 });
    }

    return NextResponse.json({
      message: 'Device unregistered successfully',
      deviceId
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Sync Unregister Device] Error:', error);
    return NextResponse.json({ 
      error: 'Device unregistration failed' 
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}