'use client';

import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { AutonomousSyncDebugger } from '@/components/debug/AutonomousSyncDebugger';

function P2PDebugPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto py-6 px-4 max-w-6xl">
      {/* Navigation */}
      <button
        onClick={() => router.back()}
        className="mb-6 flex items-center text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
        type="button"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </button>

      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">P2P Debug Console</h1>
        <p className="text-muted-foreground">
          Real-time monitoring of autonomous HTTP discovery and sync
        </p>
      </div>

      {/* Debug Interface */}
      <AutonomousSyncDebugger />
    </div>
  );
}

export default P2PDebugPage;
