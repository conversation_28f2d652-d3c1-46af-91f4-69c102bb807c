'use client';

import React, { useState, useEffect } from 'react';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';
import { 
  ArrowLeft, 
  Activity, 
  Wifi, 
  Server, 
  Smartphone, 
  Monitor,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Database,
  Play,
  Square
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

function P2PSyncPage() {
  const router = useRouter();
  const { db: mainDbInstance, isReady } = useUnifiedDB();
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  
  // Initialize autonomous sync
  const autonomousSync = useAutonomousSync(mainDbInstance, {
    enabled: true,
    autoSyncDatabases: ['orders', 'staff', 'inventory', 'settings']
  });

  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
  }, []);

  const getStatusColor = (phase: string) => {
    switch (phase) {
      case 'discovering':
      case 'connecting':
        return 'text-blue-500';
      case 'connected':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusIcon = (phase: string) => {
    switch (phase) {
      case 'discovering':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'connecting':
        return <AlertCircle className="h-4 w-4" />;
      case 'connected':
        return <CheckCircle className="h-4 w-4" />;
      case 'error':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 max-w-4xl">
      {/* Navigation */}
      <button
        onClick={() => router.back()}
        className="mb-6 flex items-center text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
        type="button"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </button>

      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">P2P Sync</h1>
          <p className="text-muted-foreground">
            HTTP-based device discovery and sync
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {isMobile && <Smartphone className="h-5 w-5 text-blue-500" />}
          {isDesktop && <Monitor className="h-5 w-5 text-purple-500" />}
          <Badge variant="outline">{platform}</Badge>
        </div>
      </div>

      {/* Status Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            Sync Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Current Status */}
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={getStatusColor(autonomousSync.autonomousState.phase)}>
                  {getStatusIcon(autonomousSync.autonomousState.phase)}
                </div>
                <div>
                  <div className="font-medium">
                    {autonomousSync.autonomousState.phase.charAt(0).toUpperCase() + 
                     autonomousSync.autonomousState.phase.slice(1)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {autonomousSync.statusMessage}
                  </div>
                </div>
              </div>
              <Badge variant={autonomousSync.isAutonomousActive ? 'default' : 'secondary'}>
                {autonomousSync.isAutonomousActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>

            {/* Controls */}
            <div className="flex space-x-2">
              <Button 
                onClick={autonomousSync.startAutonomousSync}
                disabled={autonomousSync.isAutonomousActive}
                size="sm"
              >
                <Play className="h-4 w-4 mr-2" />
                Start Sync
              </Button>
              <Button 
                onClick={autonomousSync.stopAutonomousSync}
                disabled={!autonomousSync.isAutonomousActive}
                variant="outline"
                size="sm"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop Sync
              </Button>
              <Button 
                onClick={autonomousSync.retryConnection}
                disabled={!autonomousSync.isInitialized}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Discovered Devices */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="mr-2 h-5 w-5" />
            Discovered Devices ({autonomousSync.discoveredPeers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {autonomousSync.discoveredPeers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Server className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No devices discovered</p>
              <p className="text-sm">Make sure desktop is running and on the same network</p>
            </div>
          ) : (
            <div className="space-y-3">
              {autonomousSync.discoveredPeers.map((peer) => (
                <div key={peer.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <div className="font-medium">{peer.hostname || 'Unknown Device'}</div>
                      <div className="text-sm text-muted-foreground">
                        {peer.ip}:{peer.port}
                      </div>
                    </div>
                  </div>
                  <Badge variant="outline">{peer.id.substring(0, 8)}...</Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Connected Devices */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Wifi className="mr-2 h-5 w-5" />
            Connected Devices ({autonomousSync.connectedPeers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {autonomousSync.connectedPeers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Wifi className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No active connections</p>
              <p className="text-sm">Start sync to connect to discovered devices</p>
            </div>
          ) : (
            <div className="space-y-3">
              {autonomousSync.connectedPeers.map((peer) => (
                <div key={peer.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    <div>
                      <div className="font-medium">{peer.hostname || 'Unknown Device'}</div>
                      <div className="text-sm text-muted-foreground">
                        {peer.ip}:{peer.port} • Syncing
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{peer.id.substring(0, 8)}...</Badge>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sync Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Database Sync
          </CardTitle>
        </CardHeader>
        <CardContent>
          {autonomousSync.syncStatuses.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No sync in progress</p>
              <p className="text-sm">Connect to a device to start syncing</p>
            </div>
          ) : (
            <div className="space-y-3">
              {autonomousSync.syncStatuses.map((sync) => (
                <div key={`${sync.peerId}-${sync.dbName}`} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={getStatusColor(sync.status)}>
                      {getStatusIcon(sync.status)}
                    </div>
                    <div>
                      <div className="font-medium">{sync.dbName}</div>
                      <div className="text-sm text-muted-foreground">
                        {sync.peerId.substring(0, 8)}...
                      </div>
                    </div>
                  </div>
                  <Badge variant={sync.status === 'active' ? 'default' : 'secondary'}>
                    {sync.status}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Info */}
      {autonomousSync.logs.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {autonomousSync.logs.slice(0, 10).map((log, index) => (
                <div key={index} className="text-sm font-mono text-muted-foreground p-2 bg-muted rounded">
                  {log}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default P2PSyncPage;