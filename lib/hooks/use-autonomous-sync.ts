import { useState, useEffect, useCallback, useRef } from 'react';
import { PeerInfo, SyncStatus } from '@/types/p2p-sync';
import { AutonomousSyncDaemon, AutonomousSyncConfig, AutonomousSyncStatus } from '@/lib/services/autonomous-sync-daemon';
import { SimpleIPDiscovery, DiscoveredDevice } from '@/lib/services/simple-ip-discovery';

export interface UseAutonomousSyncConfig extends Partial<AutonomousSyncConfig> {
  enabled?: boolean;
  discoveryTimeout?: number;
  fallbackDelay?: number;
  autoSyncDatabases?: string[];
  preferLocalOverInternet?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface AutonomousSyncState {
  phase: 'initializing' | 'discovering' | 'connecting' | 'connected' | 'fallback' | 'error';
  isActive: boolean;
  lastError?: string;
  discoveryStartTime?: Date;
  connectionEstablishedTime?: Date;
}

export function useAutonomousSync(dbInstance: any, config: UseAutonomousSyncConfig = {}) {
  // State management
  const [isInitialized, setIsInitialized] = useState(false);
  const [isAutonomousActive, setIsAutonomousActive] = useState(false);
  const [autonomousState, setAutonomousState] = useState<AutonomousSyncState>({
    phase: 'initializing',
    isActive: false
  });
  const [discoveredDevices, setDiscoveredDevices] = useState<DiscoveredDevice[]>([]);
  const [discoveredPeers, setDiscoveredPeers] = useState<PeerInfo[]>([]);
  const [connectedPeers, setConnectedPeers] = useState<PeerInfo[]>([]);
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  const [statusMessage, setStatusMessage] = useState<string>('Initializing...');
  const [logs, setLogs] = useState<string[]>([]);
  const [robustSyncStatus, setRobustSyncStatus] = useState<any[]>([]);
  const [healthSummary, setHealthSummary] = useState<any>(null);
  const [serverInfo, setServerInfo] = useState<any[]>([]);

  // Refs for services
  const daemonRef = useRef<AutonomousSyncDaemon | null>(null);
  const discoveryManagerRef = useRef<SimpleIPDiscovery | null>(null);

  // Default configuration
  const defaultConfig: AutonomousSyncConfig = {
    enabled: config.enabled ?? true,
    localDiscoveryTimeout: config.discoveryTimeout ?? 60000,
    internetFallbackEnabled: true,
    autoSyncDatabases: config.autoSyncDatabases ?? ['orders', 'staff', 'inventory', 'settings'],
    syncDirection: 'both',
    retryInterval: config.retryDelay ?? 30000,
    maxRetries: config.retryAttempts ?? 3,
    knownPorts: [5984, 5985, 5986, 5987], // CouchDB ports
    httpScanTimeout: 2000,
    restaurantId: undefined // Will be set from auth context
  };

  // Add log entry
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - ${message}`;
    setLogs(prev => [logEntry, ...prev].slice(0, 100));
    console.log(`[AutonomousSync] ${message}`);
  }, []);

  // Establish direct PouchDB-to-CouchDB sync connections (simplified approach)
  const establishSyncConnections = useCallback(async (couchDBServers: DiscoveredDevice[]) => {
    try {
      // Check if we're in Electron/Desktop mode before attempting sync
      const isElectron = typeof window !== 'undefined' && 
                        (Boolean((window as any).IS_DESKTOP_APP) || 
                         Boolean((window as any).electronAPI) || 
                         Boolean((window as any).process?.versions?.electron));

      if (isElectron) {
        addLog('🚫 Electron/Desktop mode detected - P2P sync not supported in desktop mode');
        addLog('💡 Desktop acts as CouchDB server for mobile clients, not as sync client');
        addLog(`📊 Desktop environment flags: IS_DESKTOP_APP=${Boolean((window as any).IS_DESKTOP_APP)}, electronAPI=${Boolean((window as any).electronAPI)}, electronProcess=${Boolean((window as any).process?.versions?.electron)}`);
        console.log('[AutonomousSync] Desktop mode detected - skipping sync establishment');
        setStatusMessage('Desktop mode: Acting as CouchDB server for mobile devices');
        return;
      }

      addLog(`🔗 Starting DIRECT sync connection establishment to ${couchDBServers.length} CouchDB servers`);

      // Import the sync bridge for direct sync
      const { PouchDBSyncBridge } = await import('@/lib/services/pouchdb-sync-bridge');

      // Create a simple sync bridge instance
      const syncBridge = new PouchDBSyncBridge({
        autoSyncDatabases: [],
        syncDirection: 'both',
        retryAttempts: 3,
        retryDelay: 5000,
        batchSize: 100,
        timeout: 30000
      });

      // Set up sync bridge event listeners
      syncBridge.on('connectionEstablished', (connection: any) => {
        addLog(`✅ Direct sync connection established: ${connection.id}`);

        // Convert to PeerInfo format for UI
        const peer: PeerInfo = {
          id: connection.peerId,
          ip: connection.peerId.split(':')[0],
          port: parseInt(connection.peerId.split(':')[1]),
          hostname: connection.peerId,
          verified: true,
          lastSeen: new Date(),
          responseTime: 0
        };

        setConnectedPeers(prev => [...prev.filter(p => p.id !== peer.id), peer]);
      });

      syncBridge.on('syncError', (data: any) => {
        addLog(`❌ Sync error: ${data.error?.message || 'Unknown error'}`);
      });

      syncBridge.on('syncProgress', (data: any) => {
        addLog(`📊 Sync progress: ${data.info?.docs?.length || 0} docs synced`);
      });

      // Establish direct connections to each CouchDB server
      let successCount = 0;
      for (const server of couchDBServers) {
        try {
          const peer: PeerInfo = {
            id: `${server.ip}:${server.port}`,
            ip: server.ip,
            port: server.port,
            hostname: server.hostname,
            verified: server.isVerified,
            lastSeen: server.lastSeen,
            responseTime: server.responseTime
          };

          addLog(`🔗 Establishing DIRECT sync to ${peer.ip}:${peer.port}`);

          // Use the sync bridge directly - this bypasses all the complex daemon layers
          const connections = await syncBridge.establishSyncConnection(peer);

          if (connections.length > 0) {
            addLog(`✅ DIRECT sync established to ${peer.ip}:${peer.port} (${connections.length} connections)`);
            successCount++;
          } else {
            addLog(`❌ No sync connections created for ${peer.ip}:${peer.port}`);
          }
        } catch (error) {
          addLog(`❌ Error establishing DIRECT sync to ${server.ip}:${server.port}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      if (successCount > 0) {
        addLog(`🎉 DIRECT sync establishment completed: ${successCount}/${couchDBServers.length} servers connected`);
        setStatusMessage(`Direct sync established to ${successCount} CouchDB server(s)`);
      } else {
        addLog(`❌ No sync connections established to any CouchDB servers`);
        setStatusMessage(`Failed to establish sync connections`);
      }

    } catch (error) {
      addLog(`❌ Error during DIRECT sync establishment: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [addLog]);

  // Initialize standalone IP discovery (independent of database)
  useEffect(() => {
    try {
      // Create discovery manager (no database dependency)
      const discoveryManager = new SimpleIPDiscovery();
      discoveryManagerRef.current = discoveryManager;

      setIsInitialized(true);
      addLog('🎯 Standalone IP discovery initialized');

    } catch (error) {
      addLog(`❌ Failed to initialize IP discovery: ${error instanceof Error ? error.message : String(error)}`);
    }

    return () => {
      if (discoveryManagerRef.current) {
        discoveryManagerRef.current.stopDiscovery();
      }
      if (daemonRef.current) {
        daemonRef.current.stop();
      }
    };
  }, [addLog]);

  // Start IP discovery
  const startAutonomousSync = useCallback(async () => {
    if (!discoveryManagerRef.current || isAutonomousActive) return;

    try {
      addLog('▶️ Starting IP discovery scan');
      setIsAutonomousActive(true);
      setAutonomousState({
        phase: 'discovering',
        isActive: true,
        discoveryStartTime: new Date()
      });
      setStatusMessage('Scanning network for HTTP servers...');

      const result = await discoveryManagerRef.current.findDevices();
      
      setDiscoveredDevices(result.devices);
      addLog(`📊 IP discovery complete: ${result.devices.length} devices found in ${result.scanDuration}ms`);
      
      // Convert devices to legacy peer format for compatibility
      const peers: PeerInfo[] = result.devices.map(device => ({
        id: `${device.ip}:${device.port}`,
        ip: device.ip,
        port: device.port,
        hostname: device.hostname,
        verified: device.isVerified,
        lastSeen: device.lastSeen,
        responseTime: device.responseTime
      }));
      
      setDiscoveredPeers(peers);
      // Check if we found any CouchDB servers (excluding self)
      const couchDBServers = result.devices.filter(device => 
        (device.serverInfo?.couchdb || device.port >= 5984 && device.port <= 5987) &&
        !device.isSelf // Exclude self devices
      );
      
      addLog(`🔍 Found ${result.devices.length} total devices, ${couchDBServers.length} CouchDB servers (excluding self)`);
      
      setAutonomousState({
        phase: 'connected',
        isActive: true,
        connectionEstablishedTime: new Date()
      });
      
      if (couchDBServers.length > 0) {
        setStatusMessage(`Found ${couchDBServers.length} CouchDB server(s) - establishing sync connections...`);
        addLog(`✅ IP discovery completed successfully - found ${couchDBServers.length} CouchDB servers!`);
        
        // Now establish actual sync connections using the daemon
        await establishSyncConnections(couchDBServers);
        
        // Stop active scanning when CouchDB servers are found
        setIsAutonomousActive(false);
        setAutonomousState({
          phase: 'connected',
          isActive: false,
          connectionEstablishedTime: new Date()
        });
      } else {
        setStatusMessage(`Found ${result.devices.length} HTTP server(s) but no CouchDB servers - will auto-retry in 30s`);
        addLog(`⚠️ IP discovery completed but no CouchDB servers found - will continue auto-scanning`);
        
        // Stop current scan and let the interval restart it
        setIsAutonomousActive(false);
        setAutonomousState({
          phase: 'initializing',
          isActive: false
        });
      }
      
      addLog('✅ IP discovery scan completed');
    } catch (error) {
      addLog(`❌ Error during IP discovery: ${error instanceof Error ? error.message : String(error)}`);
      setAutonomousState({
        phase: 'error',
        isActive: false,
        lastError: error instanceof Error ? error.message : String(error)
      });
      setStatusMessage(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [isAutonomousActive, addLog]);

  // Stop IP discovery
  const stopAutonomousSync = useCallback(async () => {
    if (!discoveryManagerRef.current || !isAutonomousActive) return;

    try {
      addLog('⏹️ Stopping IP discovery (manual stop)');
      discoveryManagerRef.current.stopDiscovery();
      setIsAutonomousActive(false);
      setAutonomousState({
        phase: 'initializing',
        isActive: false
      });
      setStatusMessage('IP discovery stopped manually');
      addLog('✅ IP discovery stopped manually');
    } catch (error) {
      addLog(`❌ Error stopping IP discovery: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [isAutonomousActive, addLog]);

  // Retry IP discovery with cache clearing
  const retryConnection = useCallback(async () => {
    if (!discoveryManagerRef.current) return;

    try {
      addLog('🔄 Retrying IP discovery (clearing cache)');
      
      // Clear cache to force fresh discovery
      discoveryManagerRef.current.clearCache();
      
      // Restart discovery
      await startAutonomousSync();
      
    } catch (error) {
      addLog(`❌ Error retrying IP discovery: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [addLog, startAutonomousSync]);

  // Get discovery stats
  const getDiscoveryStats = useCallback(() => {
    return discoveryManagerRef.current?.getStats() || {
      totalAttempts: 0,
      successfulConnections: 0,
      averageResponseTime: 0,
      activeDevices: 0,
      cachedDevices: 0
    };
  }, []);

  // Get network diagnostics
  const getNetworkDiagnostics = useCallback(() => {
    return discoveryManagerRef.current?.getNetworkDiagnostics() || {
      scannedIPs: 0,
      responsiveIPs: 0,
      averageResponseTime: 0,
      errors: []
    };
  }, []);

  // Test cleartext HTTP
  const testCleartextHttp = useCallback(async (): Promise<boolean> => {
    if (!discoveryManagerRef.current) {
      addLog('❌ IP discovery manager not initialized');
      return false;
    }
    
    addLog('🧪 Testing cleartext HTTP capability...');
    try {
      const result = await discoveryManagerRef.current.testCleartextHttp();
      addLog(result ? '✅ Cleartext HTTP test passed' : '❌ Cleartext HTTP test failed');
      return result;
    } catch (error) {
      addLog(`❌ Cleartext HTTP test error: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }, [addLog]);

  // Quick subnet scan
  const quickScan = useCallback(async (subnet: string = '192.168.1') => {
    if (!discoveryManagerRef.current) {
      addLog('❌ IP discovery manager not initialized');
      return [];
    }
    
    addLog(`🚀 Starting quick scan of ${subnet}.0/24`);
    try {
      const devices = await discoveryManagerRef.current.quickScan(subnet);
      addLog(`✅ Quick scan complete: ${devices.length} devices found`);
      setDiscoveredDevices(devices);
      return devices;
    } catch (error) {
      addLog(`❌ Quick scan error: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }, [addLog]);

  // Test specific IP
  const testSpecificIP = useCallback(async (ip: string, port: number = 5984) => {
    if (!discoveryManagerRef.current) {
      addLog('❌ IP discovery manager not initialized');
      return null;
    }
    
    addLog(`🎯 Testing specific IP ${ip}:${port}`);
    try {
      const device = await discoveryManagerRef.current.testSpecificIP(ip, port);
      if (device) {
        addLog(`✅ Found device at ${ip}:${port}`);
        setDiscoveredDevices(prev => [...prev.filter(d => d.ip !== ip || d.port !== port), device]);
      } else {
        addLog(`❌ No device found at ${ip}:${port}`);
      }
      return device;
    } catch (error) {
      addLog(`❌ Test specific IP error: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }, [addLog]);

  // Auto-start scanning when no CouchDB servers are found
  useEffect(() => {
    if (!isInitialized || isAutonomousActive) return;

    // Check if we have any CouchDB servers (excluding self)
    const hasCouchDBServers = discoveredDevices.some(device => 
      (device.serverInfo?.couchdb || device.port >= 5984 && device.port <= 5987) &&
      !device.isSelf
    );

    if (!hasCouchDBServers) {
      // No CouchDB servers found, start auto-scanning
      addLog('🔄 No CouchDB servers found, starting auto-scan...');
      startAutonomousSync();
    }
  }, [isInitialized, isAutonomousActive, discoveredDevices, addLog, startAutonomousSync]);

  // Continuous scanning and health monitoring
  useEffect(() => {
    if (!isInitialized) return;

    const interval = setInterval(async () => {
      const hasCouchDBServers = discoveredDevices.some(device => 
        (device.serverInfo?.couchdb || (device.port >= 5984 && device.port <= 5987)) &&
        !device.isSelf
      );

      if (hasCouchDBServers && discoveryManagerRef.current) {
        // If we have CouchDB servers, validate they're still online
        addLog('🔍 Validating cached CouchDB servers...');
        const validDevices = await discoveryManagerRef.current.validateCachedDevices();
        
        const validCouchDBServers = validDevices.filter(device => 
          (device.serverInfo?.couchdb || (device.port >= 5984 && device.port <= 5987)) &&
          !device.isSelf
        );
        
        if (validCouchDBServers.length === 0) {
          addLog('❌ All cached CouchDB servers are offline, restarting scan...');
          startAutonomousSync();
        } else {
          addLog(`✅ ${validCouchDBServers.length} CouchDB servers still online (excluding self)`);
        }
      } else if (!hasCouchDBServers && !isAutonomousActive) {
        addLog('🔄 Continuous scan: No CouchDB servers found, restarting scan...');
        startAutonomousSync();
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [isInitialized, isAutonomousActive, discoveredDevices, addLog, startAutonomousSync]);

  // Initial auto-start when component mounts
  useEffect(() => {
    if (isInitialized && !isAutonomousActive) {
      // Start scanning immediately when initialized
      const timer = setTimeout(() => {
        addLog('🚀 Auto-starting IP discovery scan...');
        startAutonomousSync();
      }, 2000); // Start after 2 seconds

      return () => clearTimeout(timer);
    }
  }, [isInitialized, isAutonomousActive, addLog, startAutonomousSync]);

  return {
    // State
    isInitialized,
    isAutonomousActive,
    autonomousState,
    discoveredDevices,
    discoveredPeers,
    connectedPeers,
    syncStatuses,
    statusMessage,
    logs,
    robustSyncStatus,
    healthSummary,
    serverInfo,

    // Actions
    startAutonomousSync,
    stopAutonomousSync,
    retryConnection,
    testCleartextHttp,
    quickScan,
    testSpecificIP,

    // Data
    getDiscoveryStats,
    getNetworkDiagnostics,
    
    // Cache management
    getCachedDevices: () => discoveryManagerRef.current?.getCachedDevices() || [],
    clearCache: () => discoveryManagerRef.current?.clearCache(),

    // Legacy compatibility (for existing P2P sync page)
    mobileP2PSync: {
      unifiedDiscovery: {
        getDiscoveryStats,
        getStatus: () => ({
          peersFound: discoveredDevices.length,
          averageResponseTime: getDiscoveryStats().averageResponseTime,
          lastDiscovery: new Date()
        })
      }
    }
  };
}