"use client";

import { isElectronEnvironment } from './electron-db';

/**
 * Detect if we're running in a Capacitor mobile environment
 */
const isCapacitorEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for Capacitor global object
  const hasCapacitor = !!(window as any).Capacitor;
  
  // Check for mobile user agent patterns
  const isMobileUA = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  
  // Check for absence of electron indicators in mobile context
  const notElectron = !isElectronEnvironment() && !(window as any).electronAPI;
  
  return hasCapacitor || (isMobileUA && notElectron);
};

/**
 * Initialize PouchDB in the client environment
 */
export const initPouchDB = async () => {
  // Only run in the browser
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Enhanced environment logging for debugging
    const isMobile = isCapacitorEnvironment();
    const electronFlags = {
      isDesktopApp: isElectronEnvironment(),
      IS_DESKTOP_APP: typeof window !== 'undefined' ? Boolean((window as any).IS_DESKTOP_APP) : false,
      electronAPI: typeof window !== 'undefined' ? Boolean((window as any).electronAPI) : false,
      process: typeof window !== 'undefined' && (window as any).process ? Boolean((window as any).process.versions?.electron) : false,
      isMobile,
      hasCapacitor: typeof window !== 'undefined' ? Boolean((window as any).Capacitor) : false,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown'
    };
    
    console.log('🔍 PouchDB Init - Environment Detection:', electronFlags);
    
    // Check if PouchDB is already initialized
    if ((window as any).PouchDB) {
      console.log('✅ PouchDB already loaded');
      return (window as any).PouchDB;
    }
    
    // Check if we're in Electron environment
    const isDesktopApp = electronFlags.isDesktopApp;
    
    // Mobile Capacitor-specific loading with fallbacks
    if (isMobile) {
      console.log('📱 Mobile Capacitor environment detected, using mobile-optimized PouchDB loading...');
      
      try {
        // First attempt: Try dynamic import with timeout for mobile
        console.log('📱 Attempting PouchDB dynamic import for mobile...');
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Mobile PouchDB import timeout')), 8000);
        });
        
        const importPromise = (async () => {
          const PouchDBModule = await import('pouchdb');
          const PouchDB = PouchDBModule.default || PouchDBModule;
          
          // Load pouchdb-find plugin
          const PouchDBFindModule = await import('pouchdb-find');
          const PouchDBFind = PouchDBFindModule.default || PouchDBFindModule;
          PouchDB.plugin(PouchDBFind);
          
          // Verify replication methods are available (they should be in the main PouchDB package)
          const testDb = new PouchDB('test-sync-capabilities', { adapter: 'memory' });
          const hasReplication = typeof testDb.replicate === 'object' && 
                               typeof testDb.replicate.to === 'function' && 
                               typeof testDb.replicate.from === 'function';
          const hasSync = typeof testDb.sync === 'function';
          
          await testDb.destroy(); // Clean up test database
          
          console.log('📱 ✅ PouchDB sync capabilities check:', {
            hasReplication,
            hasSync,
            hasReplicate: typeof testDb.replicate === 'object'
          });
          
          return PouchDB;
        })();
        
        const PouchDB = await Promise.race([importPromise, timeoutPromise]);
        
        // Store PouchDB on window for reuse
        (window as any).PouchDB = PouchDB;
        console.log('📱 ✅ PouchDB library loaded successfully in mobile environment');
        
        return PouchDB;
        
      } catch (mobileError) {
        console.warn('📱 ⚠️ Mobile PouchDB import failed, trying fallback methods:', mobileError);
        
        // Fallback for mobile: Check if libraries are pre-loaded globally
        if ((window as any).PouchDB) {
          console.log('📱 ✅ Found pre-loaded global PouchDB in mobile environment');
          return (window as any).PouchDB;
        }
        
        // Final fallback: Throw specific mobile error
        console.error('📱 ❌ All mobile PouchDB loading methods failed');
        throw new Error('Mobile PouchDB initialization failed - library not available in Capacitor environment');
      }
    }
    
    // Desktop/Browser loading (existing logic)
    let PouchDBModule;
    try {
      // Import core PouchDB - works in desktop/browser environments
      PouchDBModule = await import('pouchdb'); 
      // Handle ESM or CommonJS format
      const PouchDB = PouchDBModule.default || PouchDBModule;
      
      // Import and apply pouchdb-find
      const PouchDBFindModule = await import('pouchdb-find');
      const PouchDBFind = PouchDBFindModule.default || PouchDBFindModule;
      PouchDB.plugin(PouchDBFind);
      
      // Verify replication methods are available (they should be in the main PouchDB package)
      const testDb = new PouchDB('test-sync-capabilities', { adapter: 'memory' });
      const hasReplication = typeof testDb.replicate === 'object' && 
                           typeof testDb.replicate.to === 'function' && 
                           typeof testDb.replicate.from === 'function';
      const hasSync = typeof testDb.sync === 'function';
      
      await testDb.destroy(); // Clean up test database
      
      console.log('🖥️ ✅ PouchDB sync capabilities check:', {
        hasReplication,
        hasSync,
        hasReplicate: typeof testDb.replicate === 'object'
      });
      
      // Store PouchDB on window for reuse
      (window as any).PouchDB = PouchDB;
      
      if (isDesktopApp) {
        console.log('🔄 PouchDB library loaded in Electron renderer. DB operations will use IPC to CouchDB.');
      } else {
        console.log('🌐 PouchDB library loaded in browser mode. Default adapter (IndexedDB) will be used.');
      }
      
      return PouchDB;
    } catch (error) {
      console.error('❌ Failed to load PouchDB in desktop/browser environment:', error);
      
      // Last resort: Check for globally available PouchDB
      if ((window as any).PouchDB) {
        console.log('✅ Found pre-loaded global PouchDB as fallback');
        return (window as any).PouchDB;
      }
      
      return null;
    }
  } catch (error) {
    console.error('❌ Error initializing PouchDB:', error);
    return null;
  }
}; 