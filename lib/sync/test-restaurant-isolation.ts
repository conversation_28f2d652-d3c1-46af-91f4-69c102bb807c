/**
 * CRITICAL: Restaurant Isolation Testing
 * 
 * This module provides comprehensive testing to ensure users can NEVER
 * accidentally sync with the wrong restaurant's database.
 */

import { validateRestaurantContext, validateDatabaseName, validateSyncOperation } from './restaurant-validation';

export interface IsolationTestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export class RestaurantIsolationTester {
  private results: IsolationTestResult[] = [];

  constructor(private serverUrl: string) {}

  // Test 1: JWT restaurant ID consistency
  async testJWTConsistency(validToken: string, expectedRestaurantId: string): Promise<IsolationTestResult> {
    const testName = 'JWT Restaurant ID Consistency';
    
    try {
      const validation = validateRestaurantContext(validToken);
      
      if (!validation.isValid) {
        return {
          testName,
          passed: false,
          error: `Restaurant context validation failed: ${validation.error}`
        };
      }

      if (validation.restaurantId !== expectedRestaurantId) {
        return {
          testName,
          passed: false,
          error: `Restaurant ID mismatch: expected ${expectedRestaurantId}, got ${validation.restaurantId}`
        };
      }

      return {
        testName,
        passed: true,
        details: { restaurantId: validation.restaurantId, source: validation.source }
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        error: `Exception: ${(error as Error).message}`
      };
    }
  }

  // Test 2: Database name validation
  async testDatabaseNameValidation(restaurantId: string): Promise<IsolationTestResult> {
    const testName = 'Database Name Validation';
    
    try {
      const validDbName = `resto-${restaurantId.replace(/[^a-zA-Z0-9_-]/g, '')}`;
      const invalidDbName = `resto-different-restaurant-id`;
      
      const validResult = validateDatabaseName(validDbName, restaurantId);
      const invalidResult = validateDatabaseName(invalidDbName, restaurantId);
      
      if (!validResult) {
        return {
          testName,
          passed: false,
          error: `Valid database name rejected: ${validDbName}`
        };
      }

      if (invalidResult) {
        return {
          testName,
          passed: false,
          error: `Invalid database name accepted: ${invalidDbName}`
        };
      }

      return {
        testName,
        passed: true,
        details: { validDbName, invalidDbName }
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        error: `Exception: ${(error as Error).message}`
      };
    }
  }

  // Test 3: Cross-restaurant access attempt
  async testCrossRestaurantAccess(token1: string, restaurantId1: string, token2: string, restaurantId2: string): Promise<IsolationTestResult> {
    const testName = 'Cross-Restaurant Access Prevention';
    
    try {
      const dbName1 = `resto-${restaurantId1.replace(/[^a-zA-Z0-9_-]/g, '')}`;
      const dbName2 = `resto-${restaurantId2.replace(/[^a-zA-Z0-9_-]/g, '')}`;
      
      // User 1 trying to access User 2's database
      const crossAccessValidation = validateSyncOperation({
        authToken: token1,
        targetDeviceId: 'test-device',
        requestedDbName: dbName2,
        userRestaurantId: restaurantId1
      });
      
      if (crossAccessValidation.isValid) {
        return {
          testName,
          passed: false,
          error: `Cross-restaurant access was allowed! User from ${restaurantId1} accessed ${dbName2}`
        };
      }

      // User 1 accessing own database (should work)
      const validAccessValidation = validateSyncOperation({
        authToken: token1,
        targetDeviceId: 'test-device',
        requestedDbName: dbName1,
        userRestaurantId: restaurantId1
      });
      
      if (!validAccessValidation.isValid) {
        return {
          testName,
          passed: false,
          error: `Valid restaurant access was blocked: ${validAccessValidation.error}`
        };
      }

      return {
        testName,
        passed: true,
        details: { 
          crossAccessBlocked: true,
          validAccessAllowed: true,
          restaurantId1,
          restaurantId2
        }
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        error: `Exception: ${(error as Error).message}`
      };
    }
  }

  // Test 4: API endpoint isolation
  async testAPIEndpointIsolation(token1: string, token2: string): Promise<IsolationTestResult> {
    const testName = 'API Endpoint Isolation';
    
    try {
      // Test device discovery isolation
      const response1 = await fetch(`${this.serverUrl}/api/sync/discover-peers`, {
        headers: { 'Authorization': `Bearer ${token1}` }
      });

      const response2 = await fetch(`${this.serverUrl}/api/sync/discover-peers`, {
        headers: { 'Authorization': `Bearer ${token2}` }
      });

      if (!response1.ok || !response2.ok) {
        return {
          testName,
          passed: false,
          error: `API requests failed: ${response1.status}, ${response2.status}`
        };
      }

      const data1 = await response1.json();
      const data2 = await response2.json();

      // Check that restaurants see different peer sets
      if (data1.restaurantId === data2.restaurantId) {
        return {
          testName,
          passed: false,
          error: `Same restaurant ID returned for different tokens: ${data1.restaurantId}`
        };
      }

      return {
        testName,
        passed: true,
        details: {
          restaurant1: data1.restaurantId,
          restaurant2: data2.restaurantId,
          peers1: data1.count,
          peers2: data2.count
        }
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        error: `Exception: ${(error as Error).message}`
      };
    }
  }

  // Test 5: Validation endpoint security check
  async testValidationEndpoint(token: string, restaurantId: string): Promise<IsolationTestResult> {
    const testName = 'Validation Endpoint Security';
    
    try {
      const validDbName = `resto-${restaurantId.replace(/[^a-zA-Z0-9_-]/g, '')}`;
      const invalidDbName = `resto-different-restaurant`;
      
      const response = await fetch(`${this.serverUrl}/api/sync/validate-restaurant`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          dbName: invalidDbName,
          expectedRestaurantId: restaurantId
        })
      });

      if (!response.ok) {
        return {
          testName,
          passed: false,
          error: `Validation endpoint failed: ${response.status}`
        };
      }

      const result = await response.json();
      
      if (result.secure || result.validation.dbNameValid) {
        return {
          testName,
          passed: false,
          error: `Validation endpoint incorrectly validated cross-restaurant database access`
        };
      }

      return {
        testName,
        passed: true,
        details: result
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        error: `Exception: ${(error as Error).message}`
      };
    }
  }

  // Run all isolation tests
  async runAllTests(testParams: {
    token1: string;
    restaurantId1: string;
    token2: string;
    restaurantId2: string;
  }): Promise<IsolationTestResult[]> {
    const results: IsolationTestResult[] = [];

    console.log('🧪 Running Restaurant Isolation Tests...');

    // Test 1: JWT consistency for both restaurants
    results.push(await this.testJWTConsistency(testParams.token1, testParams.restaurantId1));
    results.push(await this.testJWTConsistency(testParams.token2, testParams.restaurantId2));

    // Test 2: Database name validation
    results.push(await this.testDatabaseNameValidation(testParams.restaurantId1));
    results.push(await this.testDatabaseNameValidation(testParams.restaurantId2));

    // Test 3: Cross-restaurant access prevention
    results.push(await this.testCrossRestaurantAccess(
      testParams.token1, testParams.restaurantId1,
      testParams.token2, testParams.restaurantId2
    ));

    // Test 4: API endpoint isolation
    results.push(await this.testAPIEndpointIsolation(testParams.token1, testParams.token2));

    // Test 5: Validation endpoint
    results.push(await this.testValidationEndpoint(testParams.token1, testParams.restaurantId1));
    results.push(await this.testValidationEndpoint(testParams.token2, testParams.restaurantId2));

    const passedTests = results.filter(r => r.passed).length;
    const totalTests = results.length;

    console.log(`🎯 Test Results: ${passedTests}/${totalTests} passed`);
    
    if (passedTests === totalTests) {
      console.log('✅ ALL TESTS PASSED - Restaurant isolation is secure!');
    } else {
      console.error('❌ SOME TESTS FAILED - Restaurant isolation may be compromised!');
      results.filter(r => !r.passed).forEach(test => {
        console.error(`❌ ${test.testName}: ${test.error}`);
      });
    }

    return results;
  }
}

// Export for use in applications
export function createIsolationTester(serverUrl: string): RestaurantIsolationTester {
  return new RestaurantIsolationTester(serverUrl);
}