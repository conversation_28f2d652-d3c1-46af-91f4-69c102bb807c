/**
 * Internet Sync Setup Helper
 * 
 * This module provides helper functions to set up internet sync for both
 * mobile and desktop applications using the new secure tunneling approach.
 */

import { isMobileApp, isElectronApp } from '../utils/environment';

// Server configuration
export interface InternetSyncServerConfig {
  serverUrl: string;
  authToken: string;
}

/**
 * Configure internet sync for mobile devices
 * Call this after initializing MobileP2PSync
 */
export function configureMobileInternetSync(
  mobileP2PSync: any, 
  config: InternetSyncServerConfig
): void {
  if (!isMobileApp()) {
    console.warn('configureMobileInternetSync should only be called on mobile devices');
    return;
  }

  // Configure internet sync as fallback
  mobileP2PSync.configureInternetSync({
    enabled: true,
    serverUrl: config.serverUrl,
    authToken: config.authToken,
    fallbackDelay: 5000 // Wait 5 seconds before falling back to internet
  });

  console.log('[InternetSync] Mobile internet sync configured');
}

/**
 * Configure internet sync for desktop devices (Electron)
 * Call this after P2P sync is initialized
 */
export function configureDesktopInternetSync(config: InternetSyncServerConfig): void {
  if (!isElectronApp()) {
    console.warn('configureDesktopInternetSync should only be called on Electron devices');
    return;
  }

  // Use IPC to configure internet sync in the main process
  if (window && (window as any).electronAPI) {
    (window as any).electronAPI.invoke('p2p-configure-internet-sync', {
      enabled: true,
      serverUrl: config.serverUrl,
      authToken: config.authToken
    });

    console.log('[InternetSync] Desktop internet sync configured');
  } else {
    console.error('[InternetSync] Electron API not available');
  }
}

/**
 * Example usage function for setting up internet sync
 */
export async function setupInternetSyncExample(): Promise<void> {
  // Example server configuration
  const serverConfig: InternetSyncServerConfig = {
    serverUrl: 'https://your-bistro-server.com', // Your deployed Next.js server
    authToken: 'your-jwt-token-here' // Valid JWT token for the restaurant
  };

  try {
    if (isMobileApp()) {
      // Mobile setup example
      console.log('[InternetSync] Setting up mobile internet sync...');
      
      // Assume you have a MobileP2PSync instance
      // const mobileSync = new MobileP2PSync(deviceId);
      // await mobileSync.initialize(pouchDbInstance);
      // configureMobileInternetSync(mobileSync, serverConfig);
      
      console.log('[InternetSync] Mobile setup complete');
      
    } else if (isElectronApp()) {
      // Desktop setup example
      console.log('[InternetSync] Setting up desktop internet sync...');
      
      configureDesktopInternetSync(serverConfig);
      
      console.log('[InternetSync] Desktop setup complete');
      
    } else {
      console.log('[InternetSync] Web browser - internet sync not needed');
    }
    
  } catch (error) {
    console.error('[InternetSync] Setup failed:', error);
  }
}

/**
 * Check internet sync status
 */
export async function getInternetSyncStatus(): Promise<any> {
  if (isElectronApp() && window && (window as any).electronAPI) {
    return await (window as any).electronAPI.invoke('p2p-get-internet-sync-status');
  }
  
  return { enabled: false, message: 'Not supported on this platform' };
}

/**
 * Disable internet sync
 */
export async function disableInternetSync(): Promise<void> {
  if (isElectronApp() && window && (window as any).electronAPI) {
    await (window as any).electronAPI.invoke('p2p-configure-internet-sync', {
      enabled: false,
      serverUrl: '',
      authToken: ''
    });
    
    console.log('[InternetSync] Internet sync disabled');
  }
}