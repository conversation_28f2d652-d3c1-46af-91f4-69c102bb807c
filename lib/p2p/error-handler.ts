// DEPRECATED: This file is no longer used.
// The mobile app now uses SimpleIPDiscovery through AutonomousSyncProvider.

export interface ErrorHandlerConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitterEnabled: boolean;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number;
}

export interface RetryableError {
  code: string;
  message: string;
  retryable: boolean;
  category: 'network' | 'timeout' | 'auth' | 'resource' | 'system' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
  timestamp: Date;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: RetryableError;
  attempts: number;
  totalTime: number;
}

export class AutonomousSyncErrorHandler {
  private config: ErrorHandlerConfig;
  private circuitBreakers: Map<string, { failures: number; lastFailure: Date; isOpen: boolean }> = new Map();
  private errorHistory: RetryableError[] = [];
  private maxHistorySize = 100;

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      jitterEnabled: true,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 60000, // 1 minute
      ...config
    };
  }

  /**
   * Execute a function with retry logic and error handling
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string,
    options: {
      maxRetries?: number;
      timeout?: number;
      retryableErrors?: string[];
      customErrorHandler?: (error: any) => RetryableError;
    } = {}
  ): Promise<RetryResult<T>> {
    const startTime = Date.now();
    const maxRetries = options.maxRetries ?? this.config.maxRetries;
    let lastError: RetryableError;
    let attempts = 0;

    // Check circuit breaker
    if (this.isCircuitBreakerOpen(context)) {
      return {
        success: false,
        error: this.createError(
          'CIRCUIT_BREAKER_OPEN',
          `Circuit breaker is open for ${context}`,
          false,
          'system',
          'high'
        ),
        attempts: 0,
        totalTime: Date.now() - startTime
      };
    }

    for (attempts = 0; attempts <= maxRetries; attempts++) {
      try {
        logZeroconfMessage(`🔄 Attempting ${context} (attempt ${attempts + 1}/${maxRetries + 1})`);
        
        // Execute the operation with optional timeout
        const result = options.timeout 
          ? await this.withTimeout(operation(), options.timeout)
          : await operation();

        // Success - reset circuit breaker
        this.resetCircuitBreaker(context);
        
        logZeroconfMessage(`✅ ${context} succeeded after ${attempts + 1} attempt(s)`);
        return {
          success: true,
          result,
          attempts: attempts + 1,
          totalTime: Date.now() - startTime
        };

      } catch (error) {
        // Convert error to RetryableError
        lastError = options.customErrorHandler 
          ? options.customErrorHandler(error)
          : this.convertToRetryableError(error, context);

        // Add to error history
        this.addToErrorHistory(lastError);

        // Check if error is retryable
        if (!lastError.retryable || attempts >= maxRetries) {
          // Record failure in circuit breaker
          this.recordFailure(context);
          
          logZeroconfMessage(`❌ ${context} failed permanently: ${lastError.message}`);
          return {
            success: false,
            error: lastError,
            attempts: attempts + 1,
            totalTime: Date.now() - startTime
          };
        }

        // Calculate delay for next retry
        const delay = this.calculateDelay(attempts);
        logZeroconfMessage(`⏳ ${context} failed, retrying in ${delay}ms: ${lastError.message}`);
        
        await this.sleep(delay);
      }
    }

    // This should never be reached, but included for completeness
    return {
      success: false,
      error: lastError!,
      attempts: attempts,
      totalTime: Date.now() - startTime
    };
  }

  /**
   * Convert any error to a RetryableError
   */
  private convertToRetryableError(error: any, context: string): RetryableError {
    const errorMessage = error?.message || String(error);
    const errorCode = error?.code || 'UNKNOWN_ERROR';

    // Network errors
    if (this.isNetworkError(error, errorMessage)) {
      return this.createError(
        errorCode,
        errorMessage,
        true,
        'network',
        'medium',
        { context, originalError: error }
      );
    }

    // Timeout errors
    if (this.isTimeoutError(error, errorMessage)) {
      return this.createError(
        errorCode,
        errorMessage,
        true,
        'timeout',
        'medium',
        { context, originalError: error }
      );
    }

    // Authentication errors
    if (this.isAuthError(error, errorMessage)) {
      return this.createError(
        errorCode,
        errorMessage,
        false, // Auth errors typically require user intervention
        'auth',
        'high',
        { context, originalError: error }
      );
    }

    // Resource errors (database not found, etc.)
    if (this.isResourceError(error, errorMessage)) {
      return this.createError(
        errorCode,
        errorMessage,
        true,
        'resource',
        'medium',
        { context, originalError: error }
      );
    }

    // System errors
    if (this.isSystemError(error, errorMessage)) {
      return this.createError(
        errorCode,
        errorMessage,
        false,
        'system',
        'critical',
        { context, originalError: error }
      );
    }

    // Default to unknown error
    return this.createError(
      errorCode,
      errorMessage,
      false,
      'unknown',
      'medium',
      { context, originalError: error }
    );
  }

  /**
   * Create a RetryableError
   */
  private createError(
    code: string,
    message: string,
    retryable: boolean,
    category: RetryableError['category'],
    severity: RetryableError['severity'],
    context?: Record<string, any>
  ): RetryableError {
    return {
      code,
      message,
      retryable,
      category,
      severity,
      context,
      timestamp: new Date()
    };
  }

  /**
   * Check if error is network-related
   */
  private isNetworkError(error: any, message: string): boolean {
    const networkPatterns = [
      'network',
      'connection',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNRESET',
      'fetch',
      'NetworkError'
    ];
    
    return networkPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase()) ||
      error?.code?.includes(pattern)
    );
  }

  /**
   * Check if error is timeout-related
   */
  private isTimeoutError(error: any, message: string): boolean {
    const timeoutPatterns = [
      'timeout',
      'TIMEOUT',
      'Request timed out',
      'Connection timeout'
    ];
    
    return timeoutPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Check if error is authentication-related
   */
  private isAuthError(error: any, message: string): boolean {
    const authPatterns = [
      'unauthorized',
      'authentication',
      'auth',
      'login',
      'token',
      'credential'
    ];
    
    return authPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    ) || error?.status === 401 || error?.status === 403;
  }

  /**
   * Check if error is resource-related
   */
  private isResourceError(error: any, message: string): boolean {
    const resourcePatterns = [
      'not found',
      'database',
      'resource',
      'missing'
    ];
    
    return resourcePatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    ) || error?.status === 404;
  }

  /**
   * Check if error is system-related
   */
  private isSystemError(error: any, message: string): boolean {
    const systemPatterns = [
      'out of memory',
      'disk space',
      'system',
      'internal server error',
      'service unavailable'
    ];
    
    return systemPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    ) || error?.status === 500 || error?.status === 503;
  }

  /**
   * Calculate delay for next retry using exponential backoff
   */
  private calculateDelay(attempt: number): number {
    const baseDelay = this.config.initialDelay * Math.pow(this.config.backoffMultiplier, attempt);
    const cappedDelay = Math.min(baseDelay, this.config.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (this.config.jitterEnabled) {
      const jitter = Math.random() * 0.3 * cappedDelay; // Up to 30% jitter
      return Math.round(cappedDelay + jitter);
    }
    
    return cappedDelay;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Execute operation with timeout
   */
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Circuit breaker implementation
   */
  private isCircuitBreakerOpen(context: string): boolean {
    const breaker = this.circuitBreakers.get(context);
    if (!breaker) return false;

    if (breaker.isOpen) {
      const timeSinceLastFailure = Date.now() - breaker.lastFailure.getTime();
      if (timeSinceLastFailure > this.config.circuitBreakerTimeout) {
        // Reset circuit breaker
        this.resetCircuitBreaker(context);
        return false;
      }
      return true;
    }

    return false;
  }

  /**
   * Record failure in circuit breaker
   */
  private recordFailure(context: string): void {
    const breaker = this.circuitBreakers.get(context) || {
      failures: 0,
      lastFailure: new Date(),
      isOpen: false
    };

    breaker.failures++;
    breaker.lastFailure = new Date();

    if (breaker.failures >= this.config.circuitBreakerThreshold) {
      breaker.isOpen = true;
      logZeroconfMessage(`🔴 Circuit breaker opened for ${context} after ${breaker.failures} failures`);
    }

    this.circuitBreakers.set(context, breaker);
  }

  /**
   * Reset circuit breaker
   */
  private resetCircuitBreaker(context: string): void {
    const breaker = this.circuitBreakers.get(context);
    if (breaker) {
      breaker.failures = 0;
      breaker.isOpen = false;
      logZeroconfMessage(`🟢 Circuit breaker reset for ${context}`);
    }
  }

  /**
   * Add error to history
   */
  private addToErrorHistory(error: RetryableError): void {
    this.errorHistory.push(error);
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift();
    }
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: RetryableError[];
    circuitBreakerStates: Record<string, { failures: number; isOpen: boolean }>;
  } {
    const errorsByCategory: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};

    this.errorHistory.forEach(error => {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
    });

    const circuitBreakerStates: Record<string, { failures: number; isOpen: boolean }> = {};
    this.circuitBreakers.forEach((breaker, context) => {
      circuitBreakerStates[context] = {
        failures: breaker.failures,
        isOpen: breaker.isOpen
      };
    });

    return {
      totalErrors: this.errorHistory.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrors: this.errorHistory.slice(-10),
      circuitBreakerStates
    };
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
    logZeroconfMessage('🧹 Error history cleared');
  }

  /**
   * Reset all circuit breakers
   */
  resetAllCircuitBreakers(): void {
    this.circuitBreakers.clear();
    logZeroconfMessage('🔄 All circuit breakers reset');
  }
}