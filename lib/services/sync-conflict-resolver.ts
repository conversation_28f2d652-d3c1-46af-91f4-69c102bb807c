'use client';

import { EventEmitter } from 'events';
import { getPouchDB } from '@/lib/db/pouchdb-instance';

export interface ConflictInfo {
  id: string;
  docId: string;
  dbName: string;
  localRev: string;
  remoteRev: string;
  localDoc: any;
  remoteDoc: any;
  timestamp: Date;
  resolved: boolean;
  resolutionStrategy?: 'local_wins' | 'remote_wins' | 'merge' | 'manual';
  resolvedDoc?: any;
}

export interface ConflictResolutionRule {
  docType?: string;
  field?: string;
  strategy: 'local_wins' | 'remote_wins' | 'merge' | 'latest_timestamp' | 'custom';
  customResolver?: (local: any, remote: any) => any;
  priority: number; // Higher number = higher priority
}

export interface MergeStrategy {
  name: string;
  description: string;
  resolver: (local: any, remote: any) => any;
}

export class SyncConflictResolver extends EventEmitter {
  private conflicts: Map<string, ConflictInfo> = new Map();
  private resolutionRules: ConflictResolutionRule[] = [];
  private mergeStrategies: Map<string, MergeStrategy> = new Map();
  private autoResolveEnabled = true;

  constructor() {
    super();
    this.setupDefaultMergeStrategies();
    this.setupDefaultResolutionRules();
  }

  /**
   * Handle a sync conflict
   */
  async handleConflict(
    dbName: string,
    docId: string,
    localDoc: any,
    remoteDoc: any
  ): Promise<ConflictInfo> {
    console.log(`[ConflictResolver] Handling conflict for ${docId} in ${dbName}`);

    const conflictId = `${dbName}-${docId}-${Date.now()}`;
    const conflict: ConflictInfo = {
      id: conflictId,
      docId,
      dbName,
      localRev: localDoc._rev,
      remoteRev: remoteDoc._rev,
      localDoc,
      remoteDoc,
      timestamp: new Date(),
      resolved: false
    };

    this.conflicts.set(conflictId, conflict);
    this.emit('conflictDetected', conflict);

    // Try to auto-resolve if enabled
    if (this.autoResolveEnabled) {
      await this.attemptAutoResolution(conflict);
    }

    return conflict;
  }

  /**
   * Attempt automatic conflict resolution
   */
  private async attemptAutoResolution(conflict: ConflictInfo): Promise<void> {
    console.log(`[ConflictResolver] Attempting auto-resolution for ${conflict.docId}`);

    try {
      // Find applicable resolution rule
      const rule = this.findApplicableRule(conflict.localDoc, conflict.remoteDoc);
      
      if (rule) {
        const resolvedDoc = await this.applyResolutionRule(rule, conflict);
        if (resolvedDoc) {
          await this.resolveConflict(conflict.id, rule.strategy, resolvedDoc);
          return;
        }
      }

      // Fallback to timestamp-based resolution
      const timestampResolved = this.resolveByTimestamp(conflict.localDoc, conflict.remoteDoc);
      if (timestampResolved) {
        await this.resolveConflict(conflict.id, 'latest_timestamp', timestampResolved);
        return;
      }

      // If no automatic resolution possible, emit for manual handling
      console.log(`[ConflictResolver] Auto-resolution failed for ${conflict.docId}, requires manual intervention`);
      this.emit('manualResolutionRequired', conflict);

    } catch (error) {
      console.error(`[ConflictResolver] Error during auto-resolution:`, error);
      this.emit('resolutionError', { conflict, error });
    }
  }

  /**
   * Find applicable resolution rule for the conflict
   */
  private findApplicableRule(localDoc: any, remoteDoc: any): ConflictResolutionRule | null {
    // Sort rules by priority (highest first)
    const sortedRules = [...this.resolutionRules].sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      if (this.ruleApplies(rule, localDoc, remoteDoc)) {
        return rule;
      }
    }

    return null;
  }

  /**
   * Check if a rule applies to the given documents
   */
  private ruleApplies(rule: ConflictResolutionRule, localDoc: any, remoteDoc: any): boolean {
    // Check document type
    if (rule.docType && localDoc.type !== rule.docType) {
      return false;
    }

    // Check specific field
    if (rule.field && (!localDoc[rule.field] || !remoteDoc[rule.field])) {
      return false;
    }

    return true;
  }

  /**
   * Apply a resolution rule to resolve the conflict
   */
  private async applyResolutionRule(
    rule: ConflictResolutionRule,
    conflict: ConflictInfo
  ): Promise<any | null> {
    const { localDoc, remoteDoc } = conflict;

    switch (rule.strategy) {
      case 'local_wins':
        return localDoc;

      case 'remote_wins':
        return remoteDoc;

      case 'merge':
        return this.mergeDocuments(localDoc, remoteDoc);

      case 'latest_timestamp':
        return this.resolveByTimestamp(localDoc, remoteDoc);

      case 'custom':
        if (rule.customResolver) {
          return rule.customResolver(localDoc, remoteDoc);
        }
        break;
    }

    return null;
  }

  /**
   * Merge two documents using intelligent merging
   */
  private mergeDocuments(localDoc: any, remoteDoc: any): any {
    console.log(`[ConflictResolver] Merging documents for ${localDoc._id}`);

    // Start with local document as base
    const merged = { ...localDoc };

    // Merge fields from remote document
    for (const [key, remoteValue] of Object.entries(remoteDoc)) {
      if (key.startsWith('_')) continue; // Skip CouchDB internal fields

      const localValue = localDoc[key];

      if (localValue === undefined) {
        // Field only exists in remote, add it
        merged[key] = remoteValue;
      } else if (localValue !== remoteValue) {
        // Field exists in both but with different values
        merged[key] = this.mergeFieldValues(key, localValue, remoteValue, localDoc, remoteDoc);
      }
    }

    // Update revision to remote (CouchDB requirement)
    merged._rev = remoteDoc._rev;

    return merged;
  }

  /**
   * Merge individual field values
   */
  private mergeFieldValues(
    fieldName: string,
    localValue: any,
    remoteValue: any,
    localDoc: any,
    remoteDoc: any
  ): any {
    // Handle arrays
    if (Array.isArray(localValue) && Array.isArray(remoteValue)) {
      return this.mergeArrays(localValue, remoteValue);
    }

    // Handle objects
    if (typeof localValue === 'object' && typeof remoteValue === 'object' && 
        localValue !== null && remoteValue !== null) {
      return { ...localValue, ...remoteValue };
    }

    // Handle timestamps - prefer the latest
    if (fieldName.includes('timestamp') || fieldName.includes('date') || fieldName.includes('time')) {
      const localTime = new Date(localValue).getTime();
      const remoteTime = new Date(remoteValue).getTime();
      return localTime > remoteTime ? localValue : remoteValue;
    }

    // Handle numbers - prefer the larger value for quantities
    if (typeof localValue === 'number' && typeof remoteValue === 'number') {
      if (fieldName.includes('quantity') || fieldName.includes('count') || fieldName.includes('amount')) {
        return Math.max(localValue, remoteValue);
      }
    }

    // Default: prefer remote value (assuming it's more recent)
    return remoteValue;
  }

  /**
   * Merge two arrays intelligently
   */
  private mergeArrays(localArray: any[], remoteArray: any[]): any[] {
    // For simple arrays, combine and deduplicate
    if (localArray.every(item => typeof item !== 'object')) {
      return [...new Set([...localArray, ...remoteArray])];
    }

    // For object arrays, merge by ID if available
    const merged = [...localArray];
    
    for (const remoteItem of remoteArray) {
      if (remoteItem.id) {
        const existingIndex = merged.findIndex(item => item.id === remoteItem.id);
        if (existingIndex >= 0) {
          // Merge existing item
          merged[existingIndex] = { ...merged[existingIndex], ...remoteItem };
        } else {
          // Add new item
          merged.push(remoteItem);
        }
      } else {
        // No ID, just add if not already present
        if (!merged.some(item => JSON.stringify(item) === JSON.stringify(remoteItem))) {
          merged.push(remoteItem);
        }
      }
    }

    return merged;
  }

  /**
   * Resolve conflict by timestamp (latest wins)
   */
  private resolveByTimestamp(localDoc: any, remoteDoc: any): any | null {
    const localTimestamp = this.extractTimestamp(localDoc);
    const remoteTimestamp = this.extractTimestamp(remoteDoc);

    if (localTimestamp && remoteTimestamp) {
      return localTimestamp > remoteTimestamp ? localDoc : remoteDoc;
    }

    // If no timestamps, prefer remote (assuming it's more recent)
    return remoteDoc;
  }

  /**
   * Extract timestamp from document
   */
  private extractTimestamp(doc: any): Date | null {
    const timestampFields = ['updatedAt', 'updated_at', 'modifiedAt', 'modified_at', 'timestamp'];
    
    for (const field of timestampFields) {
      if (doc[field]) {
        const timestamp = new Date(doc[field]);
        if (!isNaN(timestamp.getTime())) {
          return timestamp;
        }
      }
    }

    return null;
  }

  /**
   * Manually resolve a conflict
   */
  async resolveConflict(
    conflictId: string,
    strategy: 'local_wins' | 'remote_wins' | 'merge' | 'manual' | 'latest_timestamp',
    resolvedDoc?: any
  ): Promise<void> {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict || conflict.resolved) {
      return;
    }

    console.log(`[ConflictResolver] Resolving conflict ${conflictId} with strategy: ${strategy}`);

    try {
      let finalDoc = resolvedDoc;

      if (!finalDoc) {
        switch (strategy) {
          case 'local_wins':
            finalDoc = conflict.localDoc;
            break;
          case 'remote_wins':
            finalDoc = conflict.remoteDoc;
            break;
          case 'merge':
            finalDoc = this.mergeDocuments(conflict.localDoc, conflict.remoteDoc);
            break;
          case 'latest_timestamp':
            finalDoc = this.resolveByTimestamp(conflict.localDoc, conflict.remoteDoc);
            break;
        }
      }

      if (finalDoc) {
        // Save the resolved document to the database
        await this.saveResolvedDocument(conflict.dbName, finalDoc);

        // Mark conflict as resolved
        conflict.resolved = true;
        conflict.resolutionStrategy = strategy;
        conflict.resolvedDoc = finalDoc;

        console.log(`[ConflictResolver] ✅ Conflict resolved for ${conflict.docId}`);
        this.emit('conflictResolved', conflict);
      }

    } catch (error) {
      console.error(`[ConflictResolver] ❌ Failed to resolve conflict ${conflictId}:`, error);
      this.emit('resolutionError', { conflict, error });
    }
  }

  /**
   * Save resolved document to database
   */
  private async saveResolvedDocument(dbName: string, doc: any): Promise<void> {
    const db = getPouchDB();
    
    try {
      await db.put(doc);
      console.log(`[ConflictResolver] Saved resolved document ${doc._id}`);
    } catch (error) {
      console.error(`[ConflictResolver] Failed to save resolved document:`, error);
      throw error;
    }
  }

  /**
   * Add a custom resolution rule
   */
  addResolutionRule(rule: ConflictResolutionRule): void {
    this.resolutionRules.push(rule);
    this.resolutionRules.sort((a, b) => b.priority - a.priority);
    console.log(`[ConflictResolver] Added resolution rule for ${rule.docType || 'all documents'}`);
  }

  /**
   * Remove a resolution rule
   */
  removeResolutionRule(index: number): void {
    if (index >= 0 && index < this.resolutionRules.length) {
      this.resolutionRules.splice(index, 1);
    }
  }

  /**
   * Get all unresolved conflicts
   */
  getUnresolvedConflicts(): ConflictInfo[] {
    return Array.from(this.conflicts.values()).filter(c => !c.resolved);
  }

  /**
   * Get all conflicts
   */
  getAllConflicts(): ConflictInfo[] {
    return Array.from(this.conflicts.values());
  }

  /**
   * Clear resolved conflicts
   */
  clearResolvedConflicts(): void {
    for (const [id, conflict] of this.conflicts) {
      if (conflict.resolved) {
        this.conflicts.delete(id);
      }
    }
  }

  /**
   * Enable/disable automatic resolution
   */
  setAutoResolve(enabled: boolean): void {
    this.autoResolveEnabled = enabled;
    console.log(`[ConflictResolver] Auto-resolution ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Setup default merge strategies
   */
  private setupDefaultMergeStrategies(): void {
    // Add default merge strategies here if needed
  }

  /**
   * Setup default resolution rules
   */
  private setupDefaultResolutionRules(): void {
    // Order documents: prefer latest timestamp
    this.addResolutionRule({
      docType: 'order',
      strategy: 'latest_timestamp',
      priority: 10
    });

    // Staff documents: prefer local changes
    this.addResolutionRule({
      docType: 'staff',
      strategy: 'local_wins',
      priority: 8
    });

    // Inventory documents: merge quantities
    this.addResolutionRule({
      docType: 'inventory',
      strategy: 'merge',
      priority: 9
    });

    // Settings: prefer remote (assuming admin changes)
    this.addResolutionRule({
      docType: 'settings',
      strategy: 'remote_wins',
      priority: 7
    });
  }
}
