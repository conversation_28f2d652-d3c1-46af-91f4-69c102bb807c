'use client';

import { EventEmitter } from 'events';
import { RobustPouchDBSync, SyncServerInfo, DatabaseSyncState } from './robust-pouchdb-sync';

export interface ErrorRecoveryConfig {
  maxRetryAttempts: number;
  baseRetryDelay: number; // ms
  maxRetryDelay: number; // ms
  backoffMultiplier: number;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number; // ms
  healthCheckInterval: number; // ms
  networkTimeoutThreshold: number; // ms
  enableJitter: boolean;
}

export interface RecoveryAttempt {
  id: string;
  serverId: string;
  dbName: string;
  attempt: number;
  timestamp: Date;
  error: string;
  nextRetryAt?: Date;
  success: boolean;
}

export interface CircuitBreakerState {
  serverId: string;
  isOpen: boolean;
  failureCount: number;
  lastFailure: Date;
  nextAttemptAt: Date;
}

export class SyncErrorRecovery extends EventEmitter {
  private config: ErrorRecoveryConfig;
  private syncManager: RobustPouchDBSync;
  private recoveryAttempts: Map<string, RecoveryAttempt[]> = new Map();
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private healthCheckInterval?: NodeJS.Timeout;
  private isActive = false;

  constructor(syncManager: RobustPouchDBSync, config: Partial<ErrorRecoveryConfig> = {}) {
    super();
    
    this.syncManager = syncManager;
    this.config = {
      maxRetryAttempts: 10,
      baseRetryDelay: 1000, // 1 second
      maxRetryDelay: 300000, // 5 minutes
      backoffMultiplier: 2,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 60000, // 1 minute
      healthCheckInterval: 30000, // 30 seconds
      networkTimeoutThreshold: 10000, // 10 seconds
      enableJitter: true,
      ...config
    };

    this.setupSyncManagerListeners();
  }

  /**
   * Start error recovery monitoring
   */
  start(): void {
    if (this.isActive) return;

    console.log('[SyncErrorRecovery] Starting error recovery monitoring...');
    this.isActive = true;

    // Start periodic health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, this.config.healthCheckInterval);

    this.emit('started');
  }

  /**
   * Stop error recovery monitoring
   */
  stop(): void {
    if (!this.isActive) return;

    console.log('[SyncErrorRecovery] Stopping error recovery monitoring...');
    this.isActive = false;

    // Clear health check interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }

    // Clear all retry timeouts
    for (const timeout of this.retryTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.retryTimeouts.clear();

    this.emit('stopped');
  }

  /**
   * Handle sync error and initiate recovery
   */
  async handleSyncError(
    serverId: string,
    dbName: string,
    error: Error,
    context: 'connection' | 'replication' | 'network' | 'auth'
  ): Promise<void> {
    console.log(`[SyncErrorRecovery] Handling ${context} error for ${dbName} on ${serverId}:`, error.message);

    const recoveryKey = `${serverId}-${dbName}`;
    
    // Check circuit breaker
    if (this.isCircuitBreakerOpen(serverId)) {
      console.log(`[SyncErrorRecovery] Circuit breaker open for ${serverId}, skipping recovery`);
      this.emit('recoverySkipped', { serverId, dbName, reason: 'circuit_breaker_open' });
      return;
    }

    // Record the error
    this.recordError(serverId, dbName, error, context);

    // Determine if error is recoverable
    const isRecoverable = this.isErrorRecoverable(error, context);
    
    if (!isRecoverable) {
      console.log(`[SyncErrorRecovery] Error is not recoverable: ${error.message}`);
      this.emit('nonRecoverableError', { serverId, dbName, error, context });
      return;
    }

    // Get current attempt count
    const attempts = this.recoveryAttempts.get(recoveryKey) || [];
    const attemptCount = attempts.length;

    if (attemptCount >= this.config.maxRetryAttempts) {
      console.log(`[SyncErrorRecovery] Max retry attempts reached for ${recoveryKey}`);
      this.openCircuitBreaker(serverId);
      this.emit('maxRetriesReached', { serverId, dbName, attempts: attemptCount });
      return;
    }

    // Schedule recovery attempt
    await this.scheduleRecoveryAttempt(serverId, dbName, attemptCount + 1, error);
  }

  /**
   * Schedule a recovery attempt with exponential backoff
   */
  private async scheduleRecoveryAttempt(
    serverId: string,
    dbName: string,
    attemptNumber: number,
    error: Error
  ): Promise<void> {
    const recoveryKey = `${serverId}-${dbName}`;
    
    // Calculate delay with exponential backoff
    let delay = Math.min(
      this.config.baseRetryDelay * Math.pow(this.config.backoffMultiplier, attemptNumber - 1),
      this.config.maxRetryDelay
    );

    // Add jitter to prevent thundering herd
    if (this.config.enableJitter) {
      delay = delay + (Math.random() * delay * 0.1); // ±10% jitter
    }

    const nextRetryAt = new Date(Date.now() + delay);

    // Record the recovery attempt
    const attempt: RecoveryAttempt = {
      id: `${recoveryKey}-${attemptNumber}-${Date.now()}`,
      serverId,
      dbName,
      attempt: attemptNumber,
      timestamp: new Date(),
      error: error.message,
      nextRetryAt,
      success: false
    };

    const attempts = this.recoveryAttempts.get(recoveryKey) || [];
    attempts.push(attempt);
    this.recoveryAttempts.set(recoveryKey, attempts);

    console.log(`[SyncErrorRecovery] Scheduling recovery attempt ${attemptNumber} for ${recoveryKey} in ${delay}ms`);

    // Clear existing timeout
    const existingTimeout = this.retryTimeouts.get(recoveryKey);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Schedule the recovery attempt
    const timeout = setTimeout(async () => {
      this.retryTimeouts.delete(recoveryKey);
      await this.executeRecoveryAttempt(attempt);
    }, delay);

    this.retryTimeouts.set(recoveryKey, timeout);
    this.emit('recoveryScheduled', { attempt, delay });
  }

  /**
   * Execute a recovery attempt
   */
  private async executeRecoveryAttempt(attempt: RecoveryAttempt): Promise<void> {
    console.log(`[SyncErrorRecovery] Executing recovery attempt ${attempt.attempt} for ${attempt.dbName} on ${attempt.serverId}`);

    try {
      // Test server connectivity first
      const servers = this.syncManager.getServers();
      const server = servers.find(s => s.id === attempt.serverId);
      
      if (!server) {
        throw new Error('Server not found');
      }

      // Check if server is healthy
      if (!server.isHealthy) {
        throw new Error('Server is marked as unhealthy');
      }

      // Attempt to restart sync for this database
      await this.restartDatabaseSync(attempt.serverId, attempt.dbName);

      // Mark attempt as successful
      attempt.success = true;
      
      // Clear recovery attempts for this key
      const recoveryKey = `${attempt.serverId}-${attempt.dbName}`;
      this.recoveryAttempts.delete(recoveryKey);

      // Reset circuit breaker
      this.resetCircuitBreaker(attempt.serverId);

      console.log(`[SyncErrorRecovery] ✅ Recovery successful for ${attempt.dbName} on ${attempt.serverId}`);
      this.emit('recoverySuccessful', attempt);

    } catch (error) {
      console.error(`[SyncErrorRecovery] ❌ Recovery attempt ${attempt.attempt} failed:`, error);
      
      // Record failure in circuit breaker
      this.recordCircuitBreakerFailure(attempt.serverId);
      
      // Schedule next attempt if not at max retries
      if (attempt.attempt < this.config.maxRetryAttempts) {
        await this.scheduleRecoveryAttempt(
          attempt.serverId,
          attempt.dbName,
          attempt.attempt + 1,
          error as Error
        );
      } else {
        this.openCircuitBreaker(attempt.serverId);
        this.emit('recoveryFailed', attempt);
      }
    }
  }

  /**
   * Restart sync for a specific database
   */
  private async restartDatabaseSync(serverId: string, dbName: string): Promise<void> {
    // This would need to be implemented based on the sync manager's API
    // For now, we'll emit an event that the sync manager can listen to
    this.emit('restartDatabaseSync', { serverId, dbName });
  }

  /**
   * Check if an error is recoverable
   */
  private isErrorRecoverable(error: Error, context: string): boolean {
    const errorMessage = error.message.toLowerCase();

    // Network errors are usually recoverable
    if (context === 'network' || 
        errorMessage.includes('network') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('connection') ||
        errorMessage.includes('econnrefused') ||
        errorMessage.includes('enotfound')) {
      return true;
    }

    // Temporary server errors are recoverable
    if (errorMessage.includes('503') || 
        errorMessage.includes('502') || 
        errorMessage.includes('504') ||
        errorMessage.includes('temporary')) {
      return true;
    }

    // Authentication errors are usually not recoverable without intervention
    if (context === 'auth' || 
        errorMessage.includes('401') || 
        errorMessage.includes('403') ||
        errorMessage.includes('unauthorized') ||
        errorMessage.includes('forbidden')) {
      return false;
    }

    // Database not found errors are not recoverable
    if (errorMessage.includes('404') || 
        errorMessage.includes('not found') ||
        errorMessage.includes('database does not exist')) {
      return false;
    }

    // Default to recoverable for unknown errors
    return true;
  }

  /**
   * Record an error for tracking
   */
  private recordError(serverId: string, dbName: string, error: Error, context: string): void {
    // This could be expanded to maintain error statistics
    console.log(`[SyncErrorRecovery] Recording ${context} error for ${dbName} on ${serverId}: ${error.message}`);
    this.emit('errorRecorded', { serverId, dbName, error, context, timestamp: new Date() });
  }

  /**
   * Check if circuit breaker is open for a server
   */
  private isCircuitBreakerOpen(serverId: string): boolean {
    const breaker = this.circuitBreakers.get(serverId);
    if (!breaker || !breaker.isOpen) {
      return false;
    }

    // Check if timeout has passed
    if (Date.now() > breaker.nextAttemptAt.getTime()) {
      // Reset circuit breaker to half-open state
      breaker.isOpen = false;
      breaker.failureCount = 0;
      console.log(`[SyncErrorRecovery] Circuit breaker reset for ${serverId}`);
      return false;
    }

    return true;
  }

  /**
   * Open circuit breaker for a server
   */
  private openCircuitBreaker(serverId: string): void {
    const breaker: CircuitBreakerState = {
      serverId,
      isOpen: true,
      failureCount: this.config.circuitBreakerThreshold,
      lastFailure: new Date(),
      nextAttemptAt: new Date(Date.now() + this.config.circuitBreakerTimeout)
    };

    this.circuitBreakers.set(serverId, breaker);
    console.log(`[SyncErrorRecovery] Circuit breaker opened for ${serverId}`);
    this.emit('circuitBreakerOpened', breaker);
  }

  /**
   * Reset circuit breaker for a server
   */
  private resetCircuitBreaker(serverId: string): void {
    this.circuitBreakers.delete(serverId);
    console.log(`[SyncErrorRecovery] Circuit breaker reset for ${serverId}`);
    this.emit('circuitBreakerReset', { serverId });
  }

  /**
   * Record a failure in the circuit breaker
   */
  private recordCircuitBreakerFailure(serverId: string): void {
    const breaker = this.circuitBreakers.get(serverId) || {
      serverId,
      isOpen: false,
      failureCount: 0,
      lastFailure: new Date(),
      nextAttemptAt: new Date()
    };

    breaker.failureCount++;
    breaker.lastFailure = new Date();

    if (breaker.failureCount >= this.config.circuitBreakerThreshold) {
      this.openCircuitBreaker(serverId);
    } else {
      this.circuitBreakers.set(serverId, breaker);
    }
  }

  /**
   * Perform periodic health checks
   */
  private async performHealthChecks(): Promise<void> {
    if (!this.isActive) return;

    console.log('[SyncErrorRecovery] Performing health checks...');

    // Check for stuck recovery attempts
    const now = Date.now();
    for (const [key, attempts] of this.recoveryAttempts) {
      const lastAttempt = attempts[attempts.length - 1];
      if (lastAttempt && lastAttempt.nextRetryAt && now > lastAttempt.nextRetryAt.getTime() + 60000) {
        console.warn(`[SyncErrorRecovery] Stuck recovery attempt detected for ${key}`);
        this.emit('stuckRecoveryDetected', { key, lastAttempt });
      }
    }

    // Check circuit breakers
    for (const [serverId, breaker] of this.circuitBreakers) {
      if (breaker.isOpen && now > breaker.nextAttemptAt.getTime()) {
        console.log(`[SyncErrorRecovery] Circuit breaker timeout expired for ${serverId}, attempting reset`);
        this.resetCircuitBreaker(serverId);
      }
    }

    this.emit('healthCheckCompleted');
  }

  /**
   * Get recovery statistics
   */
  getRecoveryStats(): {
    activeRecoveries: number;
    totalAttempts: number;
    successfulRecoveries: number;
    openCircuitBreakers: number;
    averageRecoveryTime: number;
  } {
    const totalAttempts = Array.from(this.recoveryAttempts.values())
      .reduce((sum, attempts) => sum + attempts.length, 0);
    
    const successfulRecoveries = Array.from(this.recoveryAttempts.values())
      .reduce((sum, attempts) => sum + attempts.filter(a => a.success).length, 0);

    return {
      activeRecoveries: this.recoveryAttempts.size,
      totalAttempts,
      successfulRecoveries,
      openCircuitBreakers: Array.from(this.circuitBreakers.values()).filter(b => b.isOpen).length,
      averageRecoveryTime: 0 // Could be calculated from attempt timestamps
    };
  }

  /**
   * Get circuit breaker states
   */
  getCircuitBreakerStates(): CircuitBreakerState[] {
    return Array.from(this.circuitBreakers.values());
  }

  /**
   * Get active recovery attempts
   */
  getActiveRecoveryAttempts(): RecoveryAttempt[] {
    return Array.from(this.recoveryAttempts.values()).flat();
  }

  /**
   * Set up listeners for sync manager events
   */
  private setupSyncManagerListeners(): void {
    this.syncManager.on('syncError', ({ dbName, error, syncState }) => {
      if (syncState.activeServer) {
        this.handleSyncError(syncState.activeServer.id, dbName, error, 'replication');
      }
    });

    this.syncManager.on('networkOffline', () => {
      console.log('[SyncErrorRecovery] Network offline detected');
      // Pause all recovery attempts until network is back
      for (const timeout of this.retryTimeouts.values()) {
        clearTimeout(timeout);
      }
      this.retryTimeouts.clear();
    });

    this.syncManager.on('networkOnline', () => {
      console.log('[SyncErrorRecovery] Network online detected, resuming recovery attempts');
      // Could restart recovery attempts here
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stop();
    this.recoveryAttempts.clear();
    this.circuitBreakers.clear();
    this.removeAllListeners();
  }
}
