'use client';

import { EventEmitter } from 'events';
import { RobustPouchDBSync, SyncServerInfo, DatabaseSyncState } from './robust-pouchdb-sync';

export interface HealthMetrics {
  timestamp: Date;
  totalServers: number;
  healthyServers: number;
  activeSyncs: number;
  totalDatabases: number;
  averageResponseTime: number;
  totalDocsTransferred: number;
  totalConflicts: number;
  networkStatus: 'online' | 'offline' | 'unstable';
  syncEfficiency: number; // 0-100%
}

export interface ConnectionHealth {
  serverId: string;
  isHealthy: boolean;
  responseTime: number;
  errorRate: number;
  lastError?: string;
  uptime: number; // percentage
  throughput: number; // docs/minute
}

export interface SyncHealthAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: Date;
  serverId?: string;
  dbName?: string;
  resolved: boolean;
}

export class SyncHealthMonitor extends EventEmitter {
  private syncManager: RobustPouchDBSync;
  private metricsHistory: HealthMetrics[] = [];
  private alerts: SyncHealthAlert[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring = false;
  private maxHistorySize = 100;
  private maxAlertsSize = 50;

  constructor(syncManager: RobustPouchDBSync) {
    super();
    this.syncManager = syncManager;
    this.setupSyncManagerListeners();
  }

  /**
   * Start health monitoring
   */
  startMonitoring(intervalMs: number = 10000): void {
    if (this.isMonitoring) return;

    console.log('[SyncHealthMonitor] Starting health monitoring...');
    this.isMonitoring = true;

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);

    // Collect initial metrics
    this.collectMetrics();
    this.emit('monitoringStarted');
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    console.log('[SyncHealthMonitor] Stopping health monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    this.emit('monitoringStopped');
  }

  /**
   * Collect current health metrics
   */
  private collectMetrics(): void {
    const servers = this.syncManager.getServers();
    const syncStates = this.syncManager.getSyncStatus();
    const syncHealth = this.syncManager.getSyncHealth();

    const healthyServers = servers.filter(s => s.isHealthy);
    const activeSyncs = syncStates.filter(s => s.isActive);

    const averageResponseTime = healthyServers.length > 0
      ? healthyServers.reduce((sum, s) => sum + s.responseTime, 0) / healthyServers.length
      : 0;

    const totalDocsTransferred = syncStates.reduce((sum, s) => sum + s.docsTransferred, 0);
    const totalConflicts = syncStates.reduce((sum, s) => sum + s.conflicts, 0);

    // Calculate sync efficiency (percentage of databases actively syncing)
    const syncEfficiency = syncStates.length > 0
      ? (activeSyncs.length / syncStates.length) * 100
      : 0;

    const metrics: HealthMetrics = {
      timestamp: new Date(),
      totalServers: servers.length,
      healthyServers: healthyServers.length,
      activeSyncs: activeSyncs.length,
      totalDatabases: syncStates.length,
      averageResponseTime,
      totalDocsTransferred,
      totalConflicts,
      networkStatus: this.getNetworkStatus(),
      syncEfficiency
    };

    // Add to history
    this.metricsHistory.push(metrics);
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory.shift();
    }

    // Analyze metrics for potential issues
    this.analyzeMetrics(metrics);

    this.emit('metricsCollected', metrics);
  }

  /**
   * Analyze metrics and generate alerts if needed
   */
  private analyzeMetrics(metrics: HealthMetrics): void {
    // Check for unhealthy servers
    if (metrics.healthyServers === 0 && metrics.totalServers > 0) {
      this.createAlert('error', 'All servers are unhealthy', undefined, undefined);
    } else if (metrics.healthyServers < metrics.totalServers / 2) {
      this.createAlert('warning', `Only ${metrics.healthyServers}/${metrics.totalServers} servers are healthy`);
    }

    // Check sync efficiency
    if (metrics.syncEfficiency < 50) {
      this.createAlert('warning', `Low sync efficiency: ${metrics.syncEfficiency.toFixed(1)}%`);
    }

    // Check for high response times
    if (metrics.averageResponseTime > 5000) {
      this.createAlert('warning', `High average response time: ${metrics.averageResponseTime}ms`);
    }

    // Check for network issues
    if (metrics.networkStatus === 'offline') {
      this.createAlert('error', 'Network is offline');
    } else if (metrics.networkStatus === 'unstable') {
      this.createAlert('warning', 'Network connection is unstable');
    }

    // Check for excessive conflicts
    if (this.metricsHistory.length >= 2) {
      const previousMetrics = this.metricsHistory[this.metricsHistory.length - 2];
      const conflictIncrease = metrics.totalConflicts - previousMetrics.totalConflicts;
      
      if (conflictIncrease > 10) {
        this.createAlert('warning', `High conflict rate: ${conflictIncrease} new conflicts`);
      }
    }
  }

  /**
   * Get current network status
   */
  private getNetworkStatus(): 'online' | 'offline' | 'unstable' {
    if (!navigator.onLine) {
      return 'offline';
    }

    // Check if we have recent metrics to determine stability
    if (this.metricsHistory.length >= 3) {
      const recentMetrics = this.metricsHistory.slice(-3);
      const healthyServerCounts = recentMetrics.map(m => m.healthyServers);
      
      // If healthy server count is fluctuating, network might be unstable
      const hasFluctuation = healthyServerCounts.some((count, index) => 
        index > 0 && Math.abs(count - healthyServerCounts[index - 1]) > 0
      );
      
      if (hasFluctuation) {
        return 'unstable';
      }
    }

    return 'online';
  }

  /**
   * Create a new alert
   */
  private createAlert(
    type: 'warning' | 'error' | 'info',
    message: string,
    serverId?: string,
    dbName?: string
  ): void {
    // Check if similar alert already exists and is unresolved
    const existingAlert = this.alerts.find(alert => 
      !alert.resolved && 
      alert.message === message && 
      alert.serverId === serverId && 
      alert.dbName === dbName
    );

    if (existingAlert) {
      return; // Don't create duplicate alerts
    }

    const alert: SyncHealthAlert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      message,
      timestamp: new Date(),
      serverId,
      dbName,
      resolved: false
    };

    this.alerts.push(alert);
    
    // Limit alerts history
    if (this.alerts.length > this.maxAlertsSize) {
      this.alerts.shift();
    }

    console.log(`[SyncHealthMonitor] ${type.toUpperCase()}: ${message}`);
    this.emit('alertCreated', alert);
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.emit('alertResolved', alert);
    }
  }

  /**
   * Get connection health for all servers
   */
  getConnectionHealth(): ConnectionHealth[] {
    const servers = this.syncManager.getServers();
    
    return servers.map(server => {
      // Calculate uptime based on recent metrics
      const recentMetrics = this.metricsHistory.slice(-10);
      const healthyCount = recentMetrics.filter(m => 
        m.healthyServers > 0 // Simplified - would need per-server tracking
      ).length;
      const uptime = recentMetrics.length > 0 ? (healthyCount / recentMetrics.length) * 100 : 0;

      // Calculate error rate
      const errorRate = server.errorCount > 0 ? (server.errorCount / 10) * 100 : 0; // Simplified

      // Calculate throughput (docs/minute) - simplified
      const throughput = this.calculateThroughput(server.id);

      return {
        serverId: server.id,
        isHealthy: server.isHealthy,
        responseTime: server.responseTime,
        errorRate: Math.min(errorRate, 100),
        uptime: Math.max(uptime, 0),
        throughput
      };
    });
  }

  /**
   * Calculate throughput for a server (simplified)
   */
  private calculateThroughput(serverId: string): number {
    // This is a simplified calculation
    // In a real implementation, you'd track per-server document transfer rates
    const recentMetrics = this.metricsHistory.slice(-6); // Last minute if collecting every 10s
    
    if (recentMetrics.length < 2) return 0;

    const oldestMetric = recentMetrics[0];
    const newestMetric = recentMetrics[recentMetrics.length - 1];
    
    const docsDiff = newestMetric.totalDocsTransferred - oldestMetric.totalDocsTransferred;
    const timeDiff = (newestMetric.timestamp.getTime() - oldestMetric.timestamp.getTime()) / 1000 / 60; // minutes
    
    return timeDiff > 0 ? docsDiff / timeDiff : 0;
  }

  /**
   * Get current health summary
   */
  getHealthSummary(): {
    overall: 'healthy' | 'warning' | 'critical';
    metrics: HealthMetrics | null;
    activeAlerts: SyncHealthAlert[];
    connectionHealth: ConnectionHealth[];
  } {
    const latestMetrics = this.metricsHistory[this.metricsHistory.length - 1] || null;
    const activeAlerts = this.alerts.filter(a => !a.resolved);
    const connectionHealth = this.getConnectionHealth();

    // Determine overall health
    let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (activeAlerts.some(a => a.type === 'error')) {
      overall = 'critical';
    } else if (activeAlerts.some(a => a.type === 'warning')) {
      overall = 'warning';
    }

    return {
      overall,
      metrics: latestMetrics,
      activeAlerts,
      connectionHealth
    };
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(): HealthMetrics[] {
    return [...this.metricsHistory];
  }

  /**
   * Get all alerts
   */
  getAlerts(): SyncHealthAlert[] {
    return [...this.alerts];
  }

  /**
   * Clear resolved alerts
   */
  clearResolvedAlerts(): void {
    this.alerts = this.alerts.filter(a => !a.resolved);
    this.emit('alertsCleared');
  }

  /**
   * Set up listeners for sync manager events
   */
  private setupSyncManagerListeners(): void {
    this.syncManager.on('syncError', ({ dbName, error }) => {
      this.createAlert('error', `Sync error for ${dbName}: ${error.message}`, undefined, dbName);
    });

    this.syncManager.on('serverFailure', ({ server, affectedDatabases }) => {
      this.createAlert('error', `Server ${server.id} failed, affecting ${affectedDatabases.length} databases`, server.id);
    });

    this.syncManager.on('reconnectionFailed', ({ dbName }) => {
      this.createAlert('error', `Failed to reconnect database: ${dbName}`, undefined, dbName);
    });

    this.syncManager.on('networkOffline', () => {
      this.createAlert('error', 'Network connection lost');
    });

    this.syncManager.on('networkOnline', () => {
      // Resolve network-related alerts
      const networkAlerts = this.alerts.filter(a => 
        !a.resolved && a.message.includes('Network')
      );
      networkAlerts.forEach(alert => this.resolveAlert(alert.id));
      
      this.createAlert('info', 'Network connection restored');
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopMonitoring();
    this.metricsHistory = [];
    this.alerts = [];
    this.removeAllListeners();
  }
}
