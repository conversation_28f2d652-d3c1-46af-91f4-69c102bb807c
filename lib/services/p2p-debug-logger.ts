/**
 * P2P Debug Logger - Centralized logging system for P2P discovery and sync operations
 * 
 * This provides real-time logging with different severity levels and categories
 * for debugging LAN discovery, mDNS, HTTP scanning, and sync operations.
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'success';
export type LogCategory = 'discovery' | 'mdns' | 'http' | 'sync' | 'network' | 'couchdb' | 'system';

export interface P2PLogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  category: LogCategory;
  message: string;
  details?: any;
  deviceType: 'desktop' | 'mobile' | 'unknown';
  source: string; // Component or function that generated the log
}

export interface P2PDebugStats {
  totalLogs: number;
  errorCount: number;
  warningCount: number;
  lastActivity: Date | null;
  categoryCounts: Record<LogCategory, number>;
  levelCounts: Record<LogLevel, number>;
}

class P2PDebugLogger {
  private logs: P2PLogEntry[] = [];
  private maxLogs: number = 1000;
  private subscribers: Array<(logs: P2PLogEntry[]) => void> = [];
  private statsSubscribers: Array<(stats: P2PDebugStats) => void> = [];
  private deviceType: 'desktop' | 'mobile' | 'unknown' = 'unknown';

  constructor() {
    this.detectDeviceType();
  }

  private detectDeviceType(): void {
    if (typeof window !== 'undefined') {
      if ((window as any).electronAPI) {
        this.deviceType = 'desktop';
      } else if ((window as any).Capacitor) {
        this.deviceType = 'mobile';
      }
    }
  }

  /**
   * Add a log entry
   */
  log(
    level: LogLevel,
    category: LogCategory,
    message: string,
    source: string,
    details?: any
  ): void {
    const entry: P2PLogEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level,
      category,
      message,
      details,
      deviceType: this.deviceType,
      source
    };

    // Add to logs array
    this.logs.unshift(entry);

    // Trim logs if exceeding max
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Console output with emoji and color
    this.outputToConsole(entry);

    // Notify subscribers
    this.notifySubscribers();
  }

  private outputToConsole(entry: P2PLogEntry): void {
    const emoji = this.getLevelEmoji(entry.level);
    const categoryTag = `[${entry.category.toUpperCase()}]`;
    const sourceTag = `[${entry.source}]`;
    const deviceTag = `[${entry.deviceType.toUpperCase()}]`;
    
    const logMessage = `${emoji} ${deviceTag} ${categoryTag} ${sourceTag} ${entry.message}`;
    
    switch (entry.level) {
      case 'error':
        console.error(logMessage, entry.details);
        break;
      case 'warn':
        console.warn(logMessage, entry.details);
        break;
      case 'success':
        console.log(`%c${logMessage}`, 'color: green; font-weight: bold', entry.details);
        break;
      case 'info':
        console.info(logMessage, entry.details);
        break;
      case 'debug':
        console.debug(logMessage, entry.details);
        break;
    }
  }

  private getLevelEmoji(level: LogLevel): string {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'success': return '✅';
      case 'info': return 'ℹ️';
      case 'debug': return '🔍';
      default: return '📝';
    }
  }

  /**
   * Convenience methods for different log levels
   */
  debug(category: LogCategory, message: string, source: string, details?: any): void {
    this.log('debug', category, message, source, details);
  }

  info(category: LogCategory, message: string, source: string, details?: any): void {
    this.log('info', category, message, source, details);
  }

  warn(category: LogCategory, message: string, source: string, details?: any): void {
    this.log('warn', category, message, source, details);
  }

  error(category: LogCategory, message: string, source: string, details?: any): void {
    this.log('error', category, message, source, details);
  }

  success(category: LogCategory, message: string, source: string, details?: any): void {
    this.log('success', category, message, source, details);
  }

  /**
   * Get all logs or filtered logs
   */
  getLogs(filter?: {
    level?: LogLevel;
    category?: LogCategory;
    deviceType?: 'desktop' | 'mobile';
    since?: Date;
    limit?: number;
  }): P2PLogEntry[] {
    let filteredLogs = [...this.logs];

    if (filter) {
      if (filter.level) {
        filteredLogs = filteredLogs.filter(log => log.level === filter.level);
      }
      if (filter.category) {
        filteredLogs = filteredLogs.filter(log => log.category === filter.category);
      }
      if (filter.deviceType) {
        filteredLogs = filteredLogs.filter(log => log.deviceType === filter.deviceType);
      }
      if (filter.since) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.since!);
      }
      if (filter.limit) {
        filteredLogs = filteredLogs.slice(0, filter.limit);
      }
    }

    return filteredLogs;
  }

  /**
   * Get debug statistics
   */
  getStats(): P2PDebugStats {
    const stats: P2PDebugStats = {
      totalLogs: this.logs.length,
      errorCount: 0,
      warningCount: 0,
      lastActivity: this.logs.length > 0 ? this.logs[0].timestamp : null,
      categoryCounts: {
        discovery: 0,
        mdns: 0,
        http: 0,
        sync: 0,
        network: 0,
        couchdb: 0,
        system: 0
      },
      levelCounts: {
        debug: 0,
        info: 0,
        warn: 0,
        error: 0,
        success: 0
      }
    };

    this.logs.forEach(log => {
      stats.categoryCounts[log.category]++;
      stats.levelCounts[log.level]++;
      
      if (log.level === 'error') stats.errorCount++;
      if (log.level === 'warn') stats.warningCount++;
    });

    return stats;
  }

  /**
   * Subscribe to log updates
   */
  subscribe(callback: (logs: P2PLogEntry[]) => void): () => void {
    this.subscribers.push(callback);
    
    // Send current logs immediately
    callback([...this.logs]);
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to stats updates
   */
  subscribeToStats(callback: (stats: P2PDebugStats) => void): () => void {
    this.statsSubscribers.push(callback);
    
    // Send current stats immediately
    callback(this.getStats());
    
    // Return unsubscribe function
    return () => {
      const index = this.statsSubscribers.indexOf(callback);
      if (index > -1) {
        this.statsSubscribers.splice(index, 1);
      }
    };
  }

  private notifySubscribers(): void {
    const currentLogs = [...this.logs];
    const currentStats = this.getStats();
    
    this.subscribers.forEach(callback => {
      try {
        callback(currentLogs);
      } catch (error) {
        console.error('Error in log subscriber:', error);
      }
    });

    this.statsSubscribers.forEach(callback => {
      try {
        callback(currentStats);
      } catch (error) {
        console.error('Error in stats subscriber:', error);
      }
    });
  }

  /**
   * Clear all logs
   */
  clear(): void {
    this.logs = [];
    this.notifySubscribers();
  }

  /**
   * Export logs as JSON
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Set maximum number of logs to keep
   */
  setMaxLogs(max: number): void {
    this.maxLogs = max;
    if (this.logs.length > max) {
      this.logs = this.logs.slice(0, max);
      this.notifySubscribers();
    }
  }
}

// Global instance
export const p2pDebugLogger = new P2PDebugLogger();

// Convenience functions for easy import
export const logP2PDebug = (category: LogCategory, message: string, source: string, details?: any) => 
  p2pDebugLogger.debug(category, message, source, details);

export const logP2PInfo = (category: LogCategory, message: string, source: string, details?: any) => 
  p2pDebugLogger.info(category, message, source, details);

export const logP2PWarn = (category: LogCategory, message: string, source: string, details?: any) => 
  p2pDebugLogger.warn(category, message, source, details);

export const logP2PError = (category: LogCategory, message: string, source: string, details?: any) => 
  p2pDebugLogger.error(category, message, source, details);

export const logP2PSuccess = (category: LogCategory, message: string, source: string, details?: any) => 
  p2pDebugLogger.success(category, message, source, details);
