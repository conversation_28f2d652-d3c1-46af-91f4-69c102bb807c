/**
 * PouchDB Sync Bridge
 * 
 * This service bridges the gap between HTTP discovery (finding CouchDB servers)
 * and actual PouchDB sync (establishing replication connections).
 * 
 * The autonomous sync finds CouchDB servers but doesn't connect to them.
 * This bridge actually starts PouchDB replication with discovered servers.
 */

import { PeerInfo } from '@/types/p2p-sync';
import { getPouchDB, isPouchDBReady, getMainDbInstance } from '@/lib/db/pouchdb-instance';
import { cleanRestaurantId, validateDbName, generateDeviceId } from '@/lib/db/db-utils';
import { p2pDebugLogger } from './p2p-debug-logger';

export interface SyncConnection {
  id: string;
  peerId: string;
  dbName: string;
  remoteUrl: string;
  status: 'connecting' | 'active' | 'paused' | 'error';
  direction: 'push' | 'pull' | 'both';
  replication?: any; // PouchDB replication object
  lastSync?: Date;
  errorCount: number;
  bytesTransferred: number;
  docsTransferred: number;
}

export interface SyncBridgeConfig {
  autoSyncDatabases: string[];
  syncDirection: 'push' | 'pull' | 'both';
  retryAttempts: number;
  retryDelay: number;
  batchSize: number;
  timeout: number;
}

export class PouchDBSyncBridge {
  private connections = new Map<string, SyncConnection>();
  private config: SyncBridgeConfig;
  private eventListeners = new Map<string, Function[]>();
  private myDeviceId: string;
  private debugInfo = new Map<string, any>();

  constructor(config: SyncBridgeConfig) {
    this.config = {
      autoSyncDatabases: [], // Will be determined dynamically based on restaurant ID
      syncDirection: 'both',
      retryAttempts: 3,
      retryDelay: 5000,
      batchSize: 100,
      timeout: 30000,
      ...config
    };
    
    this.myDeviceId = generateDeviceId();
    p2pDebugLogger.info('sync', `🔧 SyncBridge initialized with device ID: ${this.myDeviceId}`, 'SyncBridge');
  }

  /**
   * Establish sync connection to a discovered CouchDB server
   */
  async establishSyncConnection(peer: PeerInfo, databases?: string[]): Promise<SyncConnection[]> {
    p2pDebugLogger.info('sync', `🔗 Establishing sync connections to ${peer.ip}:${peer.port}`, 'SyncBridge');

    // Self-sync prevention check
    if (this.isSelfPeer(peer)) {
      p2pDebugLogger.warn('sync', `🚫 Self-sync prevention: Skipping connection to self (${peer.ip}:${peer.port})`, 'SyncBridge');
      return [];
    }

    // Enhanced database readiness check
    const dbInstance = await this.ensureDatabaseReady();
    if (!dbInstance) {
      const error = 'Local PouchDB database not ready - cannot establish sync connections';
      p2pDebugLogger.error('sync', error, 'SyncBridge');
      throw new Error(error);
    }

    // Get restaurant database name
    const restaurantDbName = await this.getRestaurantDatabaseName();
    if (!restaurantDbName) {
      p2pDebugLogger.error('sync', '❌ Database name resolution failed', 'SyncBridge');
      return [];
    }

    if (!restaurantDbName) {
      const error = 'Could not determine restaurant database name from any source';
      p2pDebugLogger.error('sync', error, 'SyncBridge', {
        authData: localStorage.getItem('auth_data') ? 'present' : 'missing'
      });
      throw new Error(error);
    }

    // Validate database name format
    if (!validateDbName(restaurantDbName)) {
      const error = `Invalid database name format: ${restaurantDbName}`;
      p2pDebugLogger.error('sync', error, 'SyncBridge');
      throw new Error(error);
    }

    p2pDebugLogger.success('sync', `✅ Using restaurant database: ${restaurantDbName}`, 'SyncBridge');

    const connections: SyncConnection[] = [];

    try {
      // First, verify the sync connection can be established
      const connection = await this.createSyncConnection(peer, restaurantDbName, dbInstance);
      
      // Verify actual sync works by testing document sync
      await this.verifySyncFunctionality(connection);
      
      connections.push(connection);
      this.connections.set(connection.id, connection);

      p2pDebugLogger.success('sync', `✅ Sync connection established and verified: ${connection.id}`, 'SyncBridge');
      this.emit('connectionEstablished', connection);
    } catch (error) {
      p2pDebugLogger.error('sync', `❌ Failed to establish sync for ${restaurantDbName}`, 'SyncBridge', error);
      this.emit('connectionError', { peer, dbName: restaurantDbName, error });
    }

    return connections;
  }

  /**
   * Check if peer is self to prevent self-sync
   */
  private isSelfPeer(peer: PeerInfo): boolean {
    // Check if peer has device ID and it matches ours
    const peerDeviceId = peer.deviceId || (peer as any).device_id;
    if (peerDeviceId && peerDeviceId === this.myDeviceId) {
      return true;
    }
    
    // Check if IP is localhost/self
    if (peer.ip === '127.0.0.1' || peer.ip === 'localhost') {
      return true;
    }
    
    // Check if peer is marked as self
    if ((peer as any).isSelf) {
      return true;
    }
    
    return false;
  }

  /**
   * Verify sync functionality by creating and syncing a test document
   */
  private async verifySyncFunctionality(connection: SyncConnection): Promise<void> {
    p2pDebugLogger.info('sync', `🧪 Verifying sync functionality for ${connection.id}`, 'SyncBridge');
    
    try {
      // Create a test document
      const testDoc = {
        _id: `sync-test-${this.myDeviceId}-${Date.now()}`,
        type: 'sync_test',
        deviceId: this.myDeviceId,
        timestamp: new Date().toISOString(),
        testData: 'sync verification test'
      };
      
      // Get local database
      const localDb = getPouchDB();
      if (!localDb) {
        throw new Error('Local database not available for sync verification');
      }
      
      // Put test document in local database
      await localDb.put(testDoc);
      p2pDebugLogger.debug('sync', `✅ Test document created: ${testDoc._id}`, 'SyncBridge');
      
      // Wait a bit for sync to occur
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Try to verify the document reached the remote database
      const remoteUrl = `${connection.remoteUrl}/${testDoc._id}`;
      const authHeaders = {
        'Authorization': 'Basic ' + btoa('admin:admin'),
        'Accept': 'application/json'
      };
      
      const response = await fetch(remoteUrl, {
        method: 'GET',
        headers: authHeaders,
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const remoteDoc = await response.json();
        p2pDebugLogger.success('sync', `✅ Sync verification successful: Test document found on remote`, 'SyncBridge');
        
        // Clean up test document
        try {
          await localDb.remove(testDoc._id, testDoc._rev);
        } catch (cleanupError) {
          p2pDebugLogger.warn('sync', 'Failed to clean up test document', 'SyncBridge', cleanupError);
        }
      } else {
        p2pDebugLogger.warn('sync', `⚠️ Sync verification inconclusive: Test document not found on remote (may sync later)`, 'SyncBridge');
      }
      
    } catch (error) {
      p2pDebugLogger.error('sync', `❌ Sync verification failed for ${connection.id}`, 'SyncBridge', error);
      // Don't throw here - connection might still work, just verification failed
    }
  }

  /**
   * Validate that a database instance has the required PouchDB methods
   */
  private validatePouchDBInstance(db: any): boolean {
    if (!db) return false;
    
    const requiredMethods = ['get', 'put', 'post', 'remove', 'allDocs', 'changes'];
    const requiredReplicateMethods = ['to', 'from'];
    
    // Check basic PouchDB methods
    for (const method of requiredMethods) {
      if (typeof db[method] !== 'function') {
        p2pDebugLogger.warn('sync', `⚠️ Database instance missing method: ${method}`, 'SyncBridge');
        return false;
      }
    }
    
    // Check replication methods
    if (typeof db.replicate !== 'object') {
      p2pDebugLogger.warn('sync', '⚠️ Database instance missing replicate object', 'SyncBridge');
      return false;
    }
    
    for (const method of requiredReplicateMethods) {
      if (typeof db.replicate[method] !== 'function') {
        p2pDebugLogger.warn('sync', `⚠️ Database instance missing replicate.${method}`, 'SyncBridge');
        return false;
      }
    }
    
    p2pDebugLogger.debug('sync', '✅ Database instance validation passed', 'SyncBridge', {
      hasSync: typeof db.sync === 'function',
      hasReplicate: typeof db.replicate === 'object',
      hasReplicateTo: typeof db.replicate?.to === 'function',
      hasReplicateFrom: typeof db.replicate?.from === 'function'
    });
    
    return true;
  }

  /**
   * Single unified database initialization method
   */
  private async ensureDatabaseReady(): Promise<PouchDB.Database | null> {
    p2pDebugLogger.debug('sync', '🔍 Ensuring database is ready...', 'SyncBridge');

    // Desktop check - exit early
    if (this.isDesktopEnvironment()) {
      p2pDebugLogger.warn('sync', '🚫 Desktop mode - P2P sync not supported', 'SyncBridge');
      return null;
    }

    // Get restaurant ID - required for all methods
    const restaurantId = await this.getRestaurantIdFromAuth();
    if (!restaurantId) {
      p2pDebugLogger.error('sync', '❌ No restaurant ID available', 'SyncBridge');
      return null;
    }

    return await this.initializeDatabase(restaurantId);
  }

  /**
   * Check if running in desktop environment
   */
  private isDesktopEnvironment(): boolean {
    return typeof window !== 'undefined' && 
           (Boolean((window as any).IS_DESKTOP_APP) || 
            Boolean((window as any).electronAPI) || 
            Boolean((window as any).process?.versions?.electron));
  }

  /**
   * Initialize database with restaurant ID
   */
  private async initializeDatabase(restaurantId: string): Promise<PouchDB.Database | null> {
    try {
      // Try main instance first
      const mainInstance = getMainDbInstance();
      if (!mainInstance.isInitialized) {
        await mainInstance.initialize(restaurantId);
      }

      if (mainInstance.isInitialized) {
        const db = mainInstance.getDatabase();
        if (db && this.validatePouchDBInstance(db)) {
          p2pDebugLogger.success('sync', '✅ Database ready via main instance', 'SyncBridge');
          return db;
        }
      }

      // Fallback: Create direct PouchDB instance
      return await this.createDirectPouchDBInstance(restaurantId);
    } catch (error) {
      p2pDebugLogger.error('sync', '❌ Database initialization failed', 'SyncBridge', error);
      return null;
    }
  }

  /**
   * Create PouchDB instance directly
   */
  private async createDirectPouchDBInstance(restaurantId: string): Promise<PouchDB.Database | null> {
    try {
      const { cleanRestaurantId } = await import('@/lib/db/db-utils');
      const { initPouchDB } = await import('@/lib/db/pouchdb-init');
      
      const cleanedId = cleanRestaurantId(restaurantId);
      const dbName = `resto-${cleanedId}`;
      
      const PouchDB = await initPouchDB();
      if (!PouchDB) {
        throw new Error('PouchDB not available');
      }
      
      const db = new PouchDB(dbName);
      
      if (this.validatePouchDBInstance(db)) {
        p2pDebugLogger.success('sync', `✅ Direct PouchDB created: ${dbName}`, 'SyncBridge');
        return db;
      }
      
      return null;
    } catch (error) {
      p2pDebugLogger.error('sync', '❌ Direct PouchDB creation failed', 'SyncBridge', error);
      return null;
    }
  }

  /**
   * Get restaurant ID from auth data
   */
  private async getRestaurantIdFromAuth(): Promise<string | null> {
    try {
      const authData = localStorage.getItem('auth_data');
      if (!authData) return null;

      const parsedData = JSON.parse(authData);
      return parsedData.restaurantId || null;
    } catch (error) {
      p2pDebugLogger.warn('sync', 'Auth data error', 'SyncBridge', error);
      return null;
    }
  }

  /**
   * Get restaurant database name from auth data
   */
  private async getRestaurantDatabaseName(): Promise<string | null> {
    const restaurantId = await this.getRestaurantIdFromAuth();
    if (!restaurantId) {
      return null;
    }

    try {
      const { cleanRestaurantId } = await import('@/lib/db/db-utils');
      const cleanedId = cleanRestaurantId(restaurantId);
      const dbName = `resto-${cleanedId}`;
      
      p2pDebugLogger.success('sync', `✅ Database name: ${dbName}`, 'SyncBridge');
      return dbName;
    } catch (error) {
      p2pDebugLogger.error('sync', '❌ Database name resolution failed', 'SyncBridge', error);
      return null;
    }
  }

  /**
   * Create a single sync connection for a database
   */
  private async createSyncConnection(peer: PeerInfo, dbName: string, localDb: PouchDB.Database): Promise<SyncConnection> {
    const connectionId = `${peer.id}-${dbName}`;
    const remoteUrl = `http://${peer.ip}:${peer.port}/${dbName}`;

    p2pDebugLogger.info('sync', `🔗 Creating sync connection: ${connectionId} -> ${remoteUrl}`, 'SyncBridge');

    const connection: SyncConnection = {
      id: connectionId,
      peerId: peer.id,
      dbName,
      remoteUrl,
      status: 'connecting',
      direction: this.config.syncDirection,
      errorCount: 0,
      bytesTransferred: 0,
      docsTransferred: 0
    };

    try {
      // Database instance is already provided and verified
      p2pDebugLogger.success('sync', '✅ Local PouchDB instance ready', 'SyncBridge');

      p2pDebugLogger.info('sync', '🧪 Testing remote CouchDB connection...', 'SyncBridge');

      // Test connection to remote CouchDB first
      await this.testRemoteConnection(remoteUrl);

      p2pDebugLogger.success('sync', '✅ Remote connection test passed, starting replication...', 'SyncBridge');

      // Start PouchDB replication (Mobile PouchDB -> Desktop CouchDB)
      const replication = this.startReplication(localDb, remoteUrl, this.config.syncDirection);
      connection.replication = replication;

      p2pDebugLogger.info('sync', '🔧 Replication object created, setting up handlers...', 'SyncBridge');

      // Set up replication event handlers
      this.setupReplicationHandlers(connection, replication);

      connection.status = 'active';
      connection.lastSync = new Date();

      p2pDebugLogger.success('sync', `🎉 Replication started successfully: ${connectionId}`, 'SyncBridge');

    } catch (error) {
      connection.status = 'error';
      connection.errorCount++;
      p2pDebugLogger.error('sync', `❌ Failed to start replication for ${connectionId}`, 'SyncBridge', error);
      throw error;
    }

    return connection;
  }

  /**
   * Test connection to remote CouchDB server
   */
  private async testRemoteConnection(remoteUrl: string): Promise<void> {
    try {
      p2pDebugLogger.info('couchdb', `🧪 Testing connection to ${remoteUrl}...`, 'SyncBridge');

      // First test basic server connectivity
      const serverUrl = remoteUrl.replace(/\/[^\/]*$/, '/');
      p2pDebugLogger.debug('couchdb', `Testing server connectivity: ${serverUrl}`, 'SyncBridge');

      const serverResponse = await fetch(serverUrl, {
        method: 'GET',
        signal: AbortSignal.timeout(this.config.timeout)
      });

      if (!serverResponse.ok) {
        const error = `Server not reachable: ${serverResponse.status} ${serverResponse.statusText}`;
        this.storeDebugInfo('server_unreachable', {
          message: error,
          serverUrl,
          status: serverResponse.status,
          statusText: serverResponse.statusText,
          timestamp: new Date().toISOString()
        });
        throw new Error(error);
      }

      const serverInfo = await serverResponse.json();
      p2pDebugLogger.success('couchdb', `✅ CouchDB server reachable`, 'SyncBridge', {
        version: serverInfo.version,
        vendor: serverInfo.vendor?.name
      });

      // Test with authentication for the specific database
      const authHeaders = {
        'Authorization': 'Basic ' + btoa('admin:admin'),
        'Accept': 'application/json'
      };

      p2pDebugLogger.info('couchdb', `🔐 Testing database access with auth: ${remoteUrl}`, 'SyncBridge');
      const dbResponse = await fetch(remoteUrl, {
        method: 'GET',
        headers: authHeaders,
        signal: AbortSignal.timeout(this.config.timeout)
      });

      p2pDebugLogger.debug('couchdb', `Database response status: ${dbResponse.status}`, 'SyncBridge');

      if (dbResponse.status === 404) {
        p2pDebugLogger.info('couchdb', `📝 Database doesn't exist yet, attempting to create it...`, 'SyncBridge');
        
        // Try to create the database
        const createResponse = await fetch(remoteUrl, {
          method: 'PUT',
          headers: authHeaders,
          signal: AbortSignal.timeout(this.config.timeout)
        });

        if (createResponse.ok) {
          p2pDebugLogger.success('couchdb', `✅ Database created successfully`, 'SyncBridge');
          this.storeDebugInfo('database_created', {
            message: 'Database created successfully',
            databaseUrl: remoteUrl,
            timestamp: new Date().toISOString()
          });
        } else {
          const createError = await createResponse.text();
          p2pDebugLogger.warn('couchdb', `⚠️ Failed to create database: ${createResponse.status} ${createResponse.statusText}`, 'SyncBridge');
          this.storeDebugInfo('database_creation_failed', {
            message: 'Failed to create database',
            databaseUrl: remoteUrl,
            status: createResponse.status,
            statusText: createResponse.statusText,
            error: createError,
            timestamp: new Date().toISOString()
          });
        }
        return;
      }

      if (!dbResponse.ok) {
        const errorText = await dbResponse.text();
        const error = `Database access failed: ${dbResponse.status} ${dbResponse.statusText} - ${errorText}`;
        this.storeDebugInfo('database_access_failed', {
          message: error,
          databaseUrl: remoteUrl,
          status: dbResponse.status,
          statusText: dbResponse.statusText,
          error: errorText,
          timestamp: new Date().toISOString()
        });
        throw new Error(error);
      }

      const dbInfo = await dbResponse.json();
      p2pDebugLogger.success('couchdb', `✅ Database accessible`, 'SyncBridge', {
        dbName: dbInfo.db_name,
        docCount: dbInfo.doc_count,
        updateSeq: dbInfo.update_seq
      });

      this.storeDebugInfo('database_accessible', {
        message: 'Database accessible',
        databaseUrl: remoteUrl,
        dbName: dbInfo.db_name,
        docCount: dbInfo.doc_count,
        updateSeq: dbInfo.update_seq,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      p2pDebugLogger.error('couchdb', `❌ Remote connection test failed for ${remoteUrl}`, 'SyncBridge', error);
      this.storeDebugInfo('connection_test_failed', {
        message: 'Remote connection test failed',
        remoteUrl,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Cannot connect to remote CouchDB: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Start PouchDB replication
   */
  private startReplication(localDb: PouchDB.Database, remoteUrl: string, direction: 'push' | 'pull' | 'both'): any {
    // Validate that the localDb instance has the required methods
    if (!localDb) {
      throw new Error('Local database instance is null or undefined');
    }

    if (typeof localDb.replicate !== 'object' || !localDb.replicate.to || !localDb.replicate.from) {
      throw new Error('Local database instance does not have replicate methods available');
    }

    if (direction === 'both' && typeof localDb.sync !== 'function') {
      p2pDebugLogger.warn('sync', '⚠️ localDb.sync not available, falling back to bidirectional replication', 'SyncBridge');
      
      // Create a bidirectional sync using separate push and pull replications
      const pushReplication = this.startReplication(localDb, remoteUrl, 'push');
      const pullReplication = this.startReplication(localDb, remoteUrl, 'pull');
      
      // Return an object that mimics the sync interface
      return {
        push: pushReplication,
        pull: pullReplication,
        on: (event: string, handler: Function) => {
          pushReplication.on(event, handler);
          pullReplication.on(event, handler);
        },
        cancel: () => {
          pushReplication.cancel();
          pullReplication.cancel();
        }
      };
    }

    const options = {
      live: true,
      retry: true,
      batch_size: this.config.batchSize,
      timeout: this.config.timeout,
      back_off_function: (delay: number) => {
        // Exponential backoff with max delay of 30 seconds
        return Math.min(delay * 2, 30000);
      },
      // Add authentication for CouchDB
      headers: {
        'Authorization': 'Basic ' + btoa('admin:admin')
      }
    };

    p2pDebugLogger.debug('sync', `🔧 Starting ${direction} replication with options:`, 'SyncBridge', {
      live: options.live,
      retry: options.retry,
      batch_size: options.batch_size,
      timeout: options.timeout,
      hasAuth: !!options.headers.Authorization
    });

    switch (direction) {
      case 'push':
        if (typeof localDb.replicate.to !== 'function') {
          throw new Error('localDb.replicate.to is not available');
        }
        return localDb.replicate.to(remoteUrl, options);
      case 'pull':
        if (typeof localDb.replicate.from !== 'function') {
          throw new Error('localDb.replicate.from is not available');
        }
        return localDb.replicate.from(remoteUrl, options);
      case 'both':
        if (typeof localDb.sync !== 'function') {
          throw new Error('localDb.sync is not available and fallback already attempted');
        }
        return localDb.sync(remoteUrl, options);
      default:
        throw new Error(`Invalid sync direction: ${direction}`);
    }
  }

  /**
   * Set up event handlers for PouchDB replication with enhanced logging
   */
  private setupReplicationHandlers(connection: SyncConnection, replication: any): void {
    p2pDebugLogger.info('sync', `🔧 Setting up replication handlers for ${connection.id}`, 'SyncBridge');

    replication.on('change', (info: any) => {
      connection.docsTransferred += info.docs?.length || 0;
      connection.lastSync = new Date();

      p2pDebugLogger.success('sync', `📊 Sync progress ${connection.id}: ${info.docs?.length || 0} docs transferred`, 'SyncBridge', {
        docsCount: info.docs?.length || 0,
        totalTransferred: connection.docsTransferred,
        direction: info.direction || 'unknown'
      });
      this.emit('syncProgress', { connection, info });
    });

    replication.on('paused', (err: any) => {
      if (err) {
        connection.status = 'error';
        connection.errorCount++;
        p2pDebugLogger.error('sync', `⏸️ Sync paused with error ${connection.id}`, 'SyncBridge', err);
        this.emit('syncError', { connection, error: err });
      } else {
        connection.status = 'paused';
        p2pDebugLogger.info('sync', `⏸️ Sync paused ${connection.id} (normal pause)`, 'SyncBridge');
        this.emit('syncPaused', connection);
      }
    });

    replication.on('active', () => {
      connection.status = 'active';
      p2pDebugLogger.success('sync', `▶️ Sync resumed/active ${connection.id}`, 'SyncBridge');
      this.emit('syncResumed', connection);
    });

    replication.on('denied', (err: any) => {
      connection.status = 'error';
      connection.errorCount++;
      p2pDebugLogger.error('sync', `🚫 Sync denied ${connection.id} - authentication or permission issue`, 'SyncBridge', err);
      this.emit('syncError', { connection, error: err });
    });

    replication.on('complete', (info: any) => {
      p2pDebugLogger.success('sync', `✅ Sync completed ${connection.id}`, 'SyncBridge', {
        docsRead: info.docs_read || 0,
        docsWritten: info.docs_written || 0,
        docWriteFailures: info.doc_write_failures || 0
      });
      this.emit('syncComplete', { connection, info });
    });

    replication.on('error', (err: any) => {
      connection.status = 'error';
      connection.errorCount++;

      // Enhanced error categorization
      const errorMessage = err?.message || String(err);
      let errorCategory = 'unknown';
      let suggestedFix = '';

      if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
        errorCategory = 'authentication';
        suggestedFix = 'Check CouchDB credentials (admin:admin)';
      } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
        errorCategory = 'network';
        suggestedFix = 'Check network connectivity and cleartext HTTP permissions';
      } else if (errorMessage.includes('conflict') || errorMessage.includes('409')) {
        errorCategory = 'conflict';
        suggestedFix = 'Document conflicts need resolution';
      } else if (errorMessage.includes('not_found') || errorMessage.includes('404')) {
        errorCategory = 'database_missing';
        suggestedFix = 'Database may need to be created on CouchDB server';
      } else if (errorMessage.includes('CORS') || errorMessage.includes('cors')) {
        errorCategory = 'cors';
        suggestedFix = 'Configure CORS settings on CouchDB server';
      } else if (errorMessage.includes('cleartext') || errorMessage.includes('http')) {
        errorCategory = 'cleartext_http';
        suggestedFix = 'Enable cleartext HTTP in Android network security config';
      }

      p2pDebugLogger.error('sync', `❌ Sync error ${connection.id} (${errorCategory})`, 'SyncBridge', {
        error: errorMessage,
        errorCount: connection.errorCount,
        category: errorCategory,
        suggestedFix,
        willRetry: connection.errorCount <= this.config.retryAttempts
      });

      // Store detailed error info for debugging
      this.storeDebugInfo(`sync_error_${connection.id}_${Date.now()}`, {
        message: 'Sync error occurred',
        connectionId: connection.id,
        remoteUrl: connection.remoteUrl,
        dbName: connection.dbName,
        errorMessage,
        errorCategory,
        suggestedFix,
        errorCount: connection.errorCount,
        timestamp: new Date().toISOString(),
        fullError: err
      });

      this.emit('syncError', { connection, error: err });

      // Auto-retry with exponential backoff
      if (connection.errorCount <= this.config.retryAttempts) {
        const delay = this.config.retryDelay * Math.pow(2, connection.errorCount - 1);
        p2pDebugLogger.info('sync', `🔄 Retrying sync ${connection.id} in ${delay}ms (attempt ${connection.errorCount}/${this.config.retryAttempts})`, 'SyncBridge');

        setTimeout(() => {
          this.retrySyncConnection(connection);
        }, delay);
      } else {
        p2pDebugLogger.error('sync', `💀 Max retry attempts reached for ${connection.id} - giving up`, 'SyncBridge');
        this.storeDebugInfo(`sync_failed_${connection.id}`, {
          message: 'Sync failed after maximum retries',
          connectionId: connection.id,
          remoteUrl: connection.remoteUrl,
          dbName: connection.dbName,
          finalErrorMessage: errorMessage,
          errorCategory,
          suggestedFix,
          totalRetries: connection.errorCount,
          timestamp: new Date().toISOString()
        });
        this.emit('syncFailed', connection);
      }
    });

    p2pDebugLogger.success('sync', `✅ Replication handlers set up for ${connection.id}`, 'SyncBridge');
  }

  /**
   * Retry a failed sync connection with enhanced logging
   */
  private async retrySyncConnection(connection: SyncConnection): Promise<void> {
    try {
      p2pDebugLogger.info('sync', `🔄 Retrying sync connection: ${connection.id}`, 'SyncBridge');

      // Cancel existing replication
      if (connection.replication) {
        p2pDebugLogger.debug('sync', 'Cancelling existing replication', 'SyncBridge');
        connection.replication.cancel();
      }

      // Test remote connection again
      p2pDebugLogger.info('sync', 'Re-testing remote connection before retry', 'SyncBridge');
      await this.testRemoteConnection(connection.remoteUrl);

      // Get fresh PouchDB instance
      const localDb = getPouchDB();
      if (!localDb) {
        throw new Error('Local PouchDB instance not available for retry');
      }

      // Start new replication
      const replication = this.startReplication(localDb, connection.remoteUrl, connection.direction);
      connection.replication = replication;
      
      // Set up handlers again
      this.setupReplicationHandlers(connection, replication);
      
      connection.status = 'active';
      console.log(`[SyncBridge] ✅ Sync connection retried successfully: ${connection.id}`);
      
    } catch (error) {
      connection.errorCount++;
      console.error(`[SyncBridge] ❌ Retry failed for ${connection.id}:`, error);
    }
  }

  /**
   * Stop sync connection
   */
  async stopSyncConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      console.warn(`[SyncBridge] Connection not found: ${connectionId}`);
      return;
    }

    try {
      if (connection.replication) {
        connection.replication.cancel();
      }
      
      connection.status = 'paused';
      this.connections.delete(connectionId);
      
      console.log(`[SyncBridge] ✅ Sync connection stopped: ${connectionId}`);
      this.emit('connectionStopped', connection);
      
    } catch (error) {
      console.error(`[SyncBridge] ❌ Error stopping sync connection ${connectionId}:`, error);
    }
  }

  /**
   * Get all active sync connections
   */
  getConnections(): SyncConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * Get connection by ID
   */
  getConnection(connectionId: string): SyncConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * Get connections for a specific peer
   */
  getConnectionsForPeer(peerId: string): SyncConnection[] {
    return Array.from(this.connections.values()).filter(conn => conn.peerId === peerId);
  }

  /**
   * Stop all sync connections
   */
  async stopAllConnections(): Promise<void> {
    const connectionIds = Array.from(this.connections.keys());
    
    for (const connectionId of connectionIds) {
      await this.stopSyncConnection(connectionId);
    }
    
    console.log(`[SyncBridge] ✅ All sync connections stopped`);
  }

  /**
   * Event system
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[SyncBridge] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Store debug information
   */
  private storeDebugInfo(key: string, info: any): void {
    this.debugInfo.set(key, { ...info, timestamp: new Date().toISOString() });
    try {
      const existingDebug = JSON.parse(localStorage.getItem('pouchdb_sync_debug') || '{}');
      existingDebug[key] = this.debugInfo.get(key);
      localStorage.setItem('pouchdb_sync_debug', JSON.stringify(existingDebug));
    } catch (error) {
      console.warn('Failed to store debug info:', error);
    }
  }

  /**
   * Get all debug information
   */
  getAllDebugInfo(): Record<string, any> {
    const result: Record<string, any> = {};
    this.debugInfo.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  /**
   * Clear debug information
   */
  clearDebugInfo(): void {
    this.debugInfo.clear();
    localStorage.removeItem('pouchdb_sync_debug');
  }

  /**
   * Force database initialization (public method for debug)
   */
  async forceDatabaseInitialization(): Promise<boolean> {
    const restaurantId = await this.getRestaurantIdFromAuth();
    if (!restaurantId) {
      return false;
    }

    const db = await this.initializeDatabase(restaurantId);
    if (!db) {
      return false;
    }

    // Test database functionality
    try {
      const testDoc = { _id: 'test-doc', type: 'test', timestamp: new Date().toISOString() };
      await db.put(testDoc);
      const retrieved = await db.get('test-doc');
      await db.remove(retrieved);
      
      p2pDebugLogger.success('sync', '✅ Database test successful', 'SyncBridge');
      return true;
    } catch (error) {
      p2pDebugLogger.error('sync', '❌ Database test failed', 'SyncBridge', error);
      return false;
    }
  }

  /**
   * Get comprehensive sync diagnostics
   */
  getSyncDiagnostics() {
    const connections = this.getConnections();
    const debugInfo = this.getAllDebugInfo();
    
    return {
      connections: connections.map(conn => ({
        id: conn.id,
        peerId: conn.peerId,
        dbName: conn.dbName,
        remoteUrl: conn.remoteUrl,
        status: conn.status,
        direction: conn.direction,
        errorCount: conn.errorCount,
        docsTransferred: conn.docsTransferred,
        lastSync: conn.lastSync
      })),
      stats: this.getStats(),
      debugInfo,
      authData: (() => {
        try {
          const authData = localStorage.getItem('auth_data');
          if (!authData) return null;
          const parsed = JSON.parse(authData);
          return {
            hasRestaurantId: !!parsed.restaurantId,
            restaurantId: parsed.restaurantId ? `${parsed.restaurantId.substring(0, 10)}...` : null,
            keys: Object.keys(parsed)
          };
        } catch (error) {
          return { error: 'Failed to parse auth_data' };
        }
      })()
    };
  }

  /**
   * Test complete sync functionality
   */
  async testSyncFunctionality(serverUrl: string): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Initialize database
      const db = await this.ensureDatabaseReady();
      if (!db) {
        return { success: false, message: 'Database not ready' };
      }
      
      // Get database name
      const restaurantDbName = await this.getRestaurantDatabaseName();
      if (!restaurantDbName) {
        return { success: false, message: 'Could not determine database name' };
      }
      
      const remoteUrl = `${serverUrl}/${restaurantDbName}`;
      
      // Test connection and create test document
      await this.testRemoteConnection(remoteUrl);
      
      const testDoc = {
        _id: `sync-test-${Date.now()}`,
        type: 'sync_test',
        timestamp: new Date().toISOString()
      };
      
      await db.put(testDoc);
      
      // Test replication
      const replication = db.replicate.to(remoteUrl, {
        live: false,
        retry: false,
        timeout: 10000,
        headers: { 'Authorization': 'Basic ' + btoa('admin:admin') }
      });
      
      await new Promise((resolve, reject) => {
        replication.on('complete', resolve);
        replication.on('error', reject);
        setTimeout(() => reject(new Error('Timeout')), 10000);
      });
      
      // Clean up
      await db.remove(testDoc);
      
      return {
        success: true,
        message: 'Sync test successful',
        details: { dbName: restaurantDbName, remoteUrl }
      };
      
    } catch (error) {
      return {
        success: false,
        message: `Sync test failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Get sync statistics
   */
  getStats() {
    const connections = this.getConnections();
    
    return {
      totalConnections: connections.length,
      activeConnections: connections.filter(c => c.status === 'active').length,
      errorConnections: connections.filter(c => c.status === 'error').length,
      totalDocsTransferred: connections.reduce((sum, c) => sum + c.docsTransferred, 0),
      totalBytesTransferred: connections.reduce((sum, c) => sum + c.bytesTransferred, 0),
      averageErrorCount: connections.length > 0 ? 
        connections.reduce((sum, c) => sum + c.errorCount, 0) / connections.length : 0
    };
  }
}