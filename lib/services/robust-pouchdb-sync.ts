'use client';

import { EventEmitter } from 'events';
import { getPouchDB, isPouchDBReady } from '@/lib/db/pouchdb-instance';
import { isMobileApp } from '@/lib/utils/environment';
// Import CapacitorHttp - will be aliased to empty stub in non-mobile builds
import { CapacitorHttp } from '@capacitor/http';
import { cleanRestaurantId } from '@/lib/db/db-utils';

export interface SyncServerInfo {
  id: string;
  ip: string;
  port: number;
  url: string;
  isHealthy: boolean;
  lastHealthCheck: Date;
  responseTime: number;
  errorCount: number;
  priority: number; // Lower number = higher priority
}

export interface DatabaseSyncState {
  dbName: string;
  isActive: boolean;
  direction: 'push' | 'pull' | 'both';
  activeServer?: SyncServerInfo;
  replication?: any;
  lastSync?: Date;
  docsTransferred: number;
  bytesTransferred: number;
  conflicts: number;
  status: 'connecting' | 'active' | 'paused' | 'error' | 'stopped';
  error?: string;
}

export interface RobustSyncConfig {
  databases: string[];
  syncDirection: 'push' | 'pull' | 'both';
  healthCheckInterval: number; // ms
  reconnectDelay: number; // ms
  maxReconnectAttempts: number;
  heartbeatInterval: number; // ms
  conflictResolution: 'local_wins' | 'remote_wins' | 'manual';
  batchSize: number;
  timeout: number;
  enableCompression: boolean;
  persistSyncState: boolean;
}

export class RobustPouchDBSync extends EventEmitter {
  private config: RobustSyncConfig;
  private servers: Map<string, SyncServerInfo> = new Map();
  private syncStates: Map<string, DatabaseSyncState> = new Map();
  private healthCheckInterval?: NodeJS.Timeout;
  private heartbeatInterval?: NodeJS.Timeout;
  private reconnectTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private isInitialized = false;
  private isRunning = false;
  private networkChangeHandler?: () => void;

  constructor(config: Partial<RobustSyncConfig> = {}) {
    super();

    this.config = {
      databases: [], // Will be determined dynamically based on restaurant ID
      syncDirection: 'both',
      healthCheckInterval: 30000, // 30 seconds
      reconnectDelay: 5000, // 5 seconds
      maxReconnectAttempts: 10,
      heartbeatInterval: 10000, // 10 seconds
      conflictResolution: 'local_wins',
      batchSize: 100,
      timeout: 30000,
      enableCompression: true,
      persistSyncState: true,
      ...config
    };

    this.setupNetworkChangeHandler();
  }

  /**
   * Initialize the sync manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('[RobustSync] Initializing robust PouchDB sync manager...');

    if (!isPouchDBReady()) {
      throw new Error('PouchDB not ready - cannot initialize sync manager');
    }

    // Get the restaurant database name
    const restaurantDbName = await this.getRestaurantDatabaseName();
    if (!restaurantDbName) {
      throw new Error('Could not determine restaurant database name');
    }

    // Update config with the actual database name
    this.config.databases = [restaurantDbName];
    console.log(`[RobustSync] Using restaurant database: ${restaurantDbName}`);

    // Initialize sync states for the restaurant database
    for (const dbName of this.config.databases) {
      this.syncStates.set(dbName, {
        dbName,
        isActive: false,
        direction: this.config.syncDirection,
        docsTransferred: 0,
        bytesTransferred: 0,
        conflicts: 0,
        status: 'stopped'
      });
    }

    // Load persisted sync state if enabled
    if (this.config.persistSyncState) {
      await this.loadPersistedState();
    }

    this.isInitialized = true;
    console.log('[RobustSync] ✅ Sync manager initialized');
    this.emit('initialized');
  }

  /**
   * Add CouchDB servers discovered by IP discovery
   */
  addServers(discoveredDevices: Array<{ip: string; port: number; serverInfo?: any}>): void {
    console.log(`[RobustSync] Adding ${discoveredDevices.length} discovered servers`);

    for (const device of discoveredDevices) {
      // Only add CouchDB servers
      if (!device.serverInfo?.couchdb && !(device.port >= 5984 && device.port <= 5987)) {
        continue;
      }

      const serverId = `${device.ip}:${device.port}`;
      const server: SyncServerInfo = {
        id: serverId,
        ip: device.ip,
        port: device.port,
        url: `http://${device.ip}:${device.port}`,
        isHealthy: true,
        lastHealthCheck: new Date(),
        responseTime: 0,
        errorCount: 0,
        priority: device.port === 5984 ? 1 : 2 // Prefer standard CouchDB port
      };

      this.servers.set(serverId, server);
      console.log(`[RobustSync] Added server: ${serverId}`);
    }

    this.emit('serversUpdated', Array.from(this.servers.values()));
  }

  /**
   * Start continuous sync for all databases
   */
  async startSync(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Sync manager not initialized');
    }

    if (this.isRunning) {
      console.log('[RobustSync] Sync already running');
      return;
    }

    console.log('[RobustSync] Starting continuous sync...');
    this.isRunning = true;

    // Start health monitoring
    this.startHealthMonitoring();

    // Start sync for each database
    for (const dbName of this.config.databases) {
      await this.startDatabaseSync(dbName);
    }

    this.emit('syncStarted');
  }

  /**
   * Stop all sync operations
   */
  async stopSync(): Promise<void> {
    console.log('[RobustSync] Stopping all sync operations...');
    this.isRunning = false;

    // Stop health monitoring
    this.stopHealthMonitoring();

    // Stop all database syncs
    for (const [dbName, syncState] of this.syncStates) {
      await this.stopDatabaseSync(dbName);
    }

    // Clear reconnect timeouts
    for (const timeout of this.reconnectTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.reconnectTimeouts.clear();

    this.emit('syncStopped');
  }

  /**
   * Start sync for a specific database
   */
  private async startDatabaseSync(dbName: string): Promise<void> {
    const syncState = this.syncStates.get(dbName);
    if (!syncState) return;

    if (syncState.isActive) {
      console.log(`[RobustSync] Sync already active for ${dbName}`);
      return;
    }

    console.log(`[RobustSync] Starting sync for database: ${dbName}`);
    syncState.status = 'connecting';

    try {
      // Find the best available server
      const server = this.selectBestServer();
      if (!server) {
        throw new Error('No healthy servers available');
      }

      // Test connection to the server
      await this.testServerConnection(server);

      // Start PouchDB replication
      await this.startReplication(dbName, server);

      syncState.isActive = true;
      syncState.activeServer = server;
      syncState.status = 'active';
      syncState.lastSync = new Date();

      console.log(`[RobustSync] ✅ Sync started for ${dbName} with server ${server.id}`);
      this.emit('databaseSyncStarted', { dbName, server });

    } catch (error) {
      console.error(`[RobustSync] ❌ Failed to start sync for ${dbName}:`, error);
      syncState.status = 'error';
      syncState.error = (error as Error).message;
      this.emit('databaseSyncError', { dbName, error });

      // Schedule reconnection attempt
      this.scheduleReconnection(dbName);
    }
  }

  /**
   * Stop sync for a specific database
   */
  private async stopDatabaseSync(dbName: string): Promise<void> {
    const syncState = this.syncStates.get(dbName);
    if (!syncState || !syncState.isActive) return;

    console.log(`[RobustSync] Stopping sync for database: ${dbName}`);

    // Cancel replication
    if (syncState.replication) {
      try {
        syncState.replication.cancel();
      } catch (error) {
        console.warn(`[RobustSync] Error canceling replication for ${dbName}:`, error);
      }
    }

    syncState.isActive = false;
    syncState.replication = undefined;
    syncState.status = 'stopped';

    // Clear reconnection timeout
    const timeout = this.reconnectTimeouts.get(dbName);
    if (timeout) {
      clearTimeout(timeout);
      this.reconnectTimeouts.delete(dbName);
    }

    this.emit('databaseSyncStopped', { dbName });
  }

  /**
   * Select the best available server based on health and priority
   */
  private selectBestServer(): SyncServerInfo | null {
    const healthyServers = Array.from(this.servers.values())
      .filter(server => server.isHealthy)
      .sort((a, b) => {
        // Sort by priority first, then by response time
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        return a.responseTime - b.responseTime;
      });

    return healthyServers[0] || null;
  }

  /**
   * Test connection to a server
   */
  private async testServerConnection(server: SyncServerInfo): Promise<void> {
    const startTime = Date.now();
    
    try {
      const url = `${server.url}/`;
      
      const response = (isMobileApp() && CapacitorHttp) 
        ? await CapacitorHttp.get({ url, connectTimeout: this.config.timeout })
        : await fetch(url, { 
            signal: AbortSignal.timeout(this.config.timeout),
            mode: 'cors'
          });

      const responseTime = Date.now() - startTime;
      
      if (((isMobileApp() && CapacitorHttp) && response.status === 200) || (!(isMobileApp() && CapacitorHttp) && response.ok)) {
        server.responseTime = responseTime;
        server.isHealthy = true;
        server.errorCount = 0;
        server.lastHealthCheck = new Date();
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
      
    } catch (error) {
      server.isHealthy = false;
      server.errorCount++;
      server.lastHealthCheck = new Date();
      throw error;
    }
  }

  /**
   * Get current sync status for all databases
   */
  getSyncStatus(): DatabaseSyncState[] {
    return Array.from(this.syncStates.values());
  }

  /**
   * Get server information
   */
  getServers(): SyncServerInfo[] {
    return Array.from(this.servers.values());
  }

  /**
   * Get overall sync health
   */
  getSyncHealth(): {
    isHealthy: boolean;
    activeConnections: number;
    totalDatabases: number;
    healthyServers: number;
    totalServers: number;
  } {
    const activeConnections = Array.from(this.syncStates.values())
      .filter(state => state.isActive).length;
    
    const healthyServers = Array.from(this.servers.values())
      .filter(server => server.isHealthy).length;

    return {
      isHealthy: activeConnections > 0 && healthyServers > 0,
      activeConnections,
      totalDatabases: this.config.databases.length,
      healthyServers,
      totalServers: this.servers.size
    };
  }

  /**
   * Start PouchDB replication for a database
   */
  private async startReplication(dbName: string, server: SyncServerInfo): Promise<void> {
    const localDb = getPouchDB();
    const remoteUrl = `${server.url}/${dbName}`;
    const syncState = this.syncStates.get(dbName)!;

    console.log(`[RobustSync] Starting replication: ${dbName} <-> ${remoteUrl}`);

    const replicationOptions = {
      live: true,
      retry: true,
      batch_size: this.config.batchSize,
      timeout: this.config.timeout,
      back_off_function: (delay: number) => {
        // Exponential backoff with max delay of 30 seconds
        return Math.min(delay * 2, 30000);
      },
      // Enable compression if supported
      ...(this.config.enableCompression && { compression: true })
    };

    let replication: any;

    // Start replication based on direction
    switch (this.config.syncDirection) {
      case 'push':
        replication = localDb.replicate.to(remoteUrl, replicationOptions);
        break;
      case 'pull':
        replication = localDb.replicate.from(remoteUrl, replicationOptions);
        break;
      case 'both':
        replication = localDb.sync(remoteUrl, replicationOptions);
        break;
    }

    // Set up event handlers
    this.setupReplicationHandlers(dbName, replication, server);

    syncState.replication = replication;
  }

  /**
   * Set up event handlers for PouchDB replication
   */
  private setupReplicationHandlers(dbName: string, replication: any, server: SyncServerInfo): void {
    const syncState = this.syncStates.get(dbName)!;

    replication.on('change', (info: any) => {
      syncState.docsTransferred += info.docs?.length || 0;
      syncState.lastSync = new Date();

      console.log(`[RobustSync] Sync progress ${dbName}: ${info.docs?.length || 0} docs`);
      this.emit('syncProgress', { dbName, info, syncState });

      // Persist state if enabled
      if (this.config.persistSyncState) {
        this.persistSyncState();
      }
    });

    replication.on('paused', (err: any) => {
      if (err) {
        console.warn(`[RobustSync] Sync paused with error for ${dbName}:`, err);
        syncState.status = 'error';
        syncState.error = err.message;
      } else {
        console.log(`[RobustSync] Sync paused for ${dbName} (waiting for changes)`);
        syncState.status = 'paused';
      }
      this.emit('syncPaused', { dbName, error: err, syncState });
    });

    replication.on('active', () => {
      console.log(`[RobustSync] Sync resumed for ${dbName}`);
      syncState.status = 'active';
      syncState.error = undefined;
      this.emit('syncResumed', { dbName, syncState });
    });

    replication.on('denied', (err: any) => {
      console.error(`[RobustSync] Sync denied for ${dbName}:`, err);
      syncState.conflicts++;
      this.handleSyncConflict(dbName, err);
    });

    replication.on('complete', (info: any) => {
      console.log(`[RobustSync] Sync completed for ${dbName}:`, info);
      this.emit('syncComplete', { dbName, info, syncState });
    });

    replication.on('error', (err: any) => {
      console.error(`[RobustSync] Sync error for ${dbName}:`, err);
      syncState.status = 'error';
      syncState.error = err.message;
      server.errorCount++;

      this.emit('syncError', { dbName, error: err, syncState });

      // Handle connection errors
      this.handleSyncError(dbName, err, server);
    });
  }

  /**
   * Handle sync errors and implement recovery strategies
   */
  private async handleSyncError(dbName: string, error: any, server: SyncServerInfo): Promise<void> {
    console.log(`[RobustSync] Handling sync error for ${dbName}:`, error.message);

    // Mark server as unhealthy if too many errors
    if (server.errorCount >= 3) {
      server.isHealthy = false;
      console.log(`[RobustSync] Marking server ${server.id} as unhealthy`);
    }

    // Stop current sync
    await this.stopDatabaseSync(dbName);

    // Schedule reconnection with exponential backoff
    this.scheduleReconnection(dbName);
  }

  /**
   * Handle sync conflicts based on resolution strategy
   */
  private handleSyncConflict(dbName: string, conflict: any): void {
    console.log(`[RobustSync] Handling conflict for ${dbName}:`, conflict);

    switch (this.config.conflictResolution) {
      case 'local_wins':
        // Local document wins - no action needed
        console.log(`[RobustSync] Conflict resolved: local wins for ${dbName}`);
        break;
      case 'remote_wins':
        // Remote document wins - would need to implement conflict resolution
        console.log(`[RobustSync] Conflict resolved: remote wins for ${dbName}`);
        break;
      case 'manual':
        // Emit event for manual resolution
        this.emit('conflictRequiresResolution', { dbName, conflict });
        break;
    }
  }

  /**
   * Schedule reconnection attempt with exponential backoff
   */
  private scheduleReconnection(dbName: string): void {
    const syncState = this.syncStates.get(dbName);
    if (!syncState || !this.isRunning) return;

    // Clear existing timeout
    const existingTimeout = this.reconnectTimeouts.get(dbName);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Calculate delay with exponential backoff
    const attempt = syncState.activeServer?.errorCount || 1;
    const delay = Math.min(this.config.reconnectDelay * Math.pow(2, attempt - 1), 60000);

    console.log(`[RobustSync] Scheduling reconnection for ${dbName} in ${delay}ms (attempt ${attempt})`);

    const timeout = setTimeout(async () => {
      this.reconnectTimeouts.delete(dbName);

      if (this.isRunning && attempt <= this.config.maxReconnectAttempts) {
        console.log(`[RobustSync] Attempting reconnection for ${dbName}`);
        await this.startDatabaseSync(dbName);
      } else {
        console.log(`[RobustSync] Max reconnection attempts reached for ${dbName}`);
        syncState.status = 'error';
        syncState.error = 'Max reconnection attempts reached';
        this.emit('reconnectionFailed', { dbName, syncState });
      }
    }, delay);

    this.reconnectTimeouts.set(dbName, timeout);
  }

  /**
   * Start health monitoring for all servers
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) return;

    console.log('[RobustSync] Starting health monitoring...');

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, this.config.healthCheckInterval);

    // Also start heartbeat monitoring
    this.startHeartbeatMonitoring();
  }

  /**
   * Stop health monitoring
   */
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }

    console.log('[RobustSync] Health monitoring stopped');
  }

  /**
   * Perform health checks on all servers
   */
  private async performHealthChecks(): Promise<void> {
    console.log('[RobustSync] Performing health checks...');

    const healthCheckPromises = Array.from(this.servers.values()).map(async (server) => {
      try {
        await this.testServerConnection(server);
        console.log(`[RobustSync] Health check passed for ${server.id}`);
      } catch (error) {
        console.warn(`[RobustSync] Health check failed for ${server.id}:`, error);

        // If server becomes unhealthy, try to reconnect affected databases
        if (server.isHealthy) {
          server.isHealthy = false;
          await this.handleServerFailure(server);
        }
      }
    });

    await Promise.allSettled(healthCheckPromises);
    this.emit('healthCheckCompleted', this.getServers());
  }

  /**
   * Handle server failure by reconnecting affected databases
   */
  private async handleServerFailure(failedServer: SyncServerInfo): Promise<void> {
    console.log(`[RobustSync] Handling failure of server ${failedServer.id}`);

    // Find databases using this server
    const affectedDatabases = Array.from(this.syncStates.entries())
      .filter(([_, state]) => state.activeServer?.id === failedServer.id)
      .map(([dbName, _]) => dbName);

    // Reconnect affected databases to other servers
    for (const dbName of affectedDatabases) {
      console.log(`[RobustSync] Reconnecting ${dbName} due to server failure`);
      await this.stopDatabaseSync(dbName);
      this.scheduleReconnection(dbName);
    }

    this.emit('serverFailure', { server: failedServer, affectedDatabases });
  }

  /**
   * Start heartbeat monitoring for active connections
   */
  private startHeartbeatMonitoring(): void {
    if (this.heartbeatInterval) return;

    this.heartbeatInterval = setInterval(async () => {
      await this.performHeartbeatChecks();
    }, this.config.heartbeatInterval);
  }

  /**
   * Perform heartbeat checks on active connections
   */
  private async performHeartbeatChecks(): Promise<void> {
    const activeSyncs = Array.from(this.syncStates.values())
      .filter(state => state.isActive && state.activeServer);

    for (const syncState of activeSyncs) {
      if (!syncState.activeServer) continue;

      try {
        // Simple ping to check if server is still responsive
        await this.testServerConnection(syncState.activeServer);
      } catch (error) {
        console.warn(`[RobustSync] Heartbeat failed for ${syncState.dbName}:`, error);

        // If heartbeat fails, the health check will handle reconnection
        syncState.activeServer.isHealthy = false;
      }
    }
  }

  /**
   * Set up network change handler
   */
  private setupNetworkChangeHandler(): void {
    if (typeof window === 'undefined') return;

    this.networkChangeHandler = () => {
      console.log('[RobustSync] Network change detected');
      this.handleNetworkChange();
    };

    window.addEventListener('online', this.networkChangeHandler);
    window.addEventListener('offline', this.networkChangeHandler);
  }

  /**
   * Handle network changes
   */
  private async handleNetworkChange(): Promise<void> {
    if (!navigator.onLine) {
      console.log('[RobustSync] Device went offline');
      this.emit('networkOffline');
      return;
    }

    console.log('[RobustSync] Device came online or changed networks');
    this.emit('networkOnline');

    // Wait a moment for network to stabilize
    setTimeout(async () => {
      if (this.isRunning) {
        console.log('[RobustSync] Reconnecting after network change...');
        await this.reconnectAll();
      }
    }, 2000);
  }

  /**
   * Reconnect all databases
   */
  private async reconnectAll(): Promise<void> {
    console.log('[RobustSync] Reconnecting all databases...');

    // Stop all current syncs
    for (const dbName of this.config.databases) {
      await this.stopDatabaseSync(dbName);
    }

    // Mark all servers as potentially healthy again
    for (const server of this.servers.values()) {
      server.errorCount = 0;
      server.isHealthy = true;
    }

    // Restart sync for all databases
    for (const dbName of this.config.databases) {
      await this.startDatabaseSync(dbName);
    }

    this.emit('reconnectionCompleted');
  }

  /**
   * Load persisted sync state
   */
  private async loadPersistedState(): Promise<void> {
    try {
      const persistedState = localStorage.getItem('robust-sync-state');
      if (persistedState) {
        const state = JSON.parse(persistedState);
        console.log('[RobustSync] Loaded persisted sync state');
        // Could restore some state here if needed
      }
    } catch (error) {
      console.warn('[RobustSync] Failed to load persisted state:', error);
    }
  }

  /**
   * Persist current sync state
   */
  private persistSyncState(): void {
    try {
      const state = {
        timestamp: new Date().toISOString(),
        syncStates: Array.from(this.syncStates.entries()).map(([dbName, state]) => ({
          dbName,
          docsTransferred: state.docsTransferred,
          bytesTransferred: state.bytesTransferred,
          conflicts: state.conflicts,
          lastSync: state.lastSync?.toISOString()
        }))
      };

      localStorage.setItem('robust-sync-state', JSON.stringify(state));
    } catch (error) {
      console.warn('[RobustSync] Failed to persist sync state:', error);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    console.log('[RobustSync] Destroying sync manager...');

    this.stopSync();

    // Remove network event listeners
    if (this.networkChangeHandler && typeof window !== 'undefined') {
      window.removeEventListener('online', this.networkChangeHandler);
      window.removeEventListener('offline', this.networkChangeHandler);
    }
  }

  /**
   * Get the restaurant database name from auth data
   */
  private async getRestaurantDatabaseName(): Promise<string | null> {
    try {
      // Get restaurant ID from auth_data (the real source of truth)
      const authData = localStorage.getItem('auth_data');
      if (!authData) {
        console.warn('[RobustSync] No auth_data found in localStorage');
        return null;
      }

      const parsedData = JSON.parse(authData);
      if (!parsedData.restaurantId) {
        console.warn('[RobustSync] No restaurantId found in auth_data');
        return null;
      }

      const cleanedRestaurantId = cleanRestaurantId(parsedData.restaurantId);
      const dbName = `resto-${cleanedRestaurantId}`;

      console.log(`[RobustSync] Restaurant database name: ${dbName} (from ID: ${parsedData.restaurantId})`);
      return dbName;
    } catch (error) {
      console.error('[RobustSync] Error getting restaurant database name:', error);
      return null;
    }

    // Clear all data
    this.servers.clear();
    this.syncStates.clear();
    this.reconnectTimeouts.clear();

    this.isInitialized = false;
    this.emit('destroyed');
  }
}
