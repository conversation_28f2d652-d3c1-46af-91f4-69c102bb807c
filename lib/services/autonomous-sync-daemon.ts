import { EventEmitter } from 'events';
import { PeerInfo, SyncStatus } from '@/types/p2p-sync';
import { SimpleIPDiscovery, SimpleDiscoveryResult } from './simple-ip-discovery';
import { PouchDBSyncBridge, SyncConnection as BridgeSyncConnection } from './pouchdb-sync-bridge';
import { RobustPouchDBSync, DatabaseSyncState } from './robust-pouchdb-sync';
import { SyncHealthMonitor } from './sync-health-monitor';
import { SyncConflictResolver } from './sync-conflict-resolver';
import { SyncErrorRecovery } from './sync-error-recovery';

export interface AutonomousSyncConfig {
  enabled: boolean;
  localDiscoveryTimeout: number; // Default: 60 seconds
  internetFallbackEnabled: boolean;
  autoSyncDatabases: string[];
  syncDirection: 'push' | 'pull' | 'both';
  retryInterval: number; // Default: 30 seconds
  maxRetries: number; // Default: 3
  knownPorts: number[]; // Default: [5984, 5985, 5986, 5987] - CouchDB ports
  httpScanTimeout: number; // Default: 2000ms per IP
  restaurantId?: string;
}

export interface AutonomousSyncStatus {
  phase: 'initializing' | 'discovering' | 'connecting' | 'connected' | 'fallback' | 'error';
  localPeers: PeerInfo[];
  internetPeers: PeerInfo[];
  activeSyncs: SyncConnection[];
  lastError?: string;
  discoveryStartTime?: Date;
  connectionEstablishedTime?: Date;
  networkDiagnostics?: NetworkDiagnostics;
}

export interface SyncConnection {
  id: string;
  peerId: string;
  dbName: string;
  status: 'connecting' | 'active' | 'paused' | 'error';
  direction: 'push' | 'pull' | 'both';
  progress?: number;
  lastSync?: Date;
  errorCount: number;
}

export interface NetworkDiagnostics {
  localIP?: string;
  subnet?: string;
  scannedIPs: number;
  responsiveIPs: number;
  averageResponseTime: number;
  lastScanTime?: Date;
  errors: string[];
}

export class AutonomousSyncDaemon extends EventEmitter {
  private config: AutonomousSyncConfig;
  private status: AutonomousSyncStatus;
  private isRunning: boolean = false;
  private discoveryInterval?: NodeJS.Timeout;
  private connectionHealthInterval?: NodeJS.Timeout;
  private networkChangeTimeout?: NodeJS.Timeout;
  private httpDiscovery: SimpleIPDiscovery;
  private syncBridge: PouchDBSyncBridge;
  private robustSync: RobustPouchDBSync;
  private healthMonitor: SyncHealthMonitor;
  private conflictResolver: SyncConflictResolver;
  private errorRecovery: SyncErrorRecovery;

  constructor(config: AutonomousSyncConfig) {
    super();
    this.config = {
      enabled: true,
      localDiscoveryTimeout: 60000,
      internetFallbackEnabled: true,
      autoSyncDatabases: [], // Will be determined dynamically based on restaurant ID
      syncDirection: 'both',
      retryInterval: 30000,
      maxRetries: 3,
      knownPorts: [5984, 5985, 5986, 5987], // CouchDB ports
      httpScanTimeout: 2000,
      ...config
    };

    this.status = {
      phase: 'initializing',
      localPeers: [],
      internetPeers: [],
      activeSyncs: [],
      networkDiagnostics: {
        scannedIPs: 0,
        responsiveIPs: 0,
        averageResponseTime: 0,
        errors: []
      }
    };

    // Initialize HTTP discovery manager
    this.httpDiscovery = new SimpleIPDiscovery();

    // Initialize PouchDB sync bridge (legacy support)
    this.syncBridge = new PouchDBSyncBridge({
      autoSyncDatabases: [], // Will be set dynamically
      syncDirection: this.config.syncDirection,
      retryAttempts: this.config.maxRetries,
      retryDelay: 5000,
      batchSize: 100,
      timeout: 30000
    });

    // Initialize robust sync manager
    this.robustSync = new RobustPouchDBSync({
      databases: [], // Will be set dynamically
      syncDirection: this.config.syncDirection,
      healthCheckInterval: 30000,
      reconnectDelay: 5000,
      maxReconnectAttempts: this.config.maxRetries,
      heartbeatInterval: 10000,
      conflictResolution: 'local_wins',
      batchSize: 100,
      timeout: 30000,
      enableCompression: true,
      persistSyncState: true
    });

    // Initialize health monitor
    this.healthMonitor = new SyncHealthMonitor(this.robustSync);

    // Initialize conflict resolver
    this.conflictResolver = new SyncConflictResolver();

    // Initialize error recovery
    this.errorRecovery = new SyncErrorRecovery(this.robustSync);

    // Set up event handlers
    this.setupSyncBridgeHandlers();
    this.setupRobustSyncHandlers();
    this.setupErrorRecoveryHandlers();
  }

  configure(config: Partial<AutonomousSyncConfig>): void {
    this.config = { ...this.config, ...config };
    this.emit('configurationChanged', this.config);
  }

  async start(): Promise<boolean> {
    if (!this.config.enabled || this.isRunning) {
      return false;
    }

    try {
      this.isRunning = true;
      this.status.phase = 'discovering';
      this.status.discoveryStartTime = new Date();

      this.emit('statusChanged', this.status);

      // Initialize robust sync components
      await this.robustSync.initialize();
      this.healthMonitor.startMonitoring();
      this.errorRecovery.start();

      // Start discovery process
      await this.startDiscovery();

      // Set up periodic health monitoring
      this.setupHealthMonitoring();

      return true;
    } catch (error) {
      this.status.phase = 'error';
      this.status.lastError = error instanceof Error ? error.message : String(error);
      this.emit('statusChanged', this.status);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Set up event handlers for robust sync manager
   */
  private setupRobustSyncHandlers(): void {
    this.robustSync.on('initialized', () => {
      console.log('[AutonomousSync] Robust sync initialized');
    });

    this.robustSync.on('syncStarted', () => {
      console.log('[AutonomousSync] Robust sync started');
      this.status.phase = 'connected';
      this.emit('statusChanged', this.status);
    });

    this.robustSync.on('syncStopped', () => {
      console.log('[AutonomousSync] Robust sync stopped');
    });

    this.robustSync.on('databaseSyncStarted', ({ dbName, server }) => {
      console.log(`[AutonomousSync] Database sync started: ${dbName} with ${server.id}`);
      this.emit('databaseSyncStarted', { dbName, server });
    });

    this.robustSync.on('databaseSyncError', ({ dbName, error }) => {
      console.error(`[AutonomousSync] Database sync error: ${dbName}`, error);
      this.emit('databaseSyncError', { dbName, error });
    });

    this.robustSync.on('syncProgress', ({ dbName, info, syncState }) => {
      this.emit('syncProgress', { dbName, info, syncState });
    });

    this.robustSync.on('networkOffline', () => {
      this.status.phase = 'error';
      this.status.lastError = 'Network offline';
      this.emit('statusChanged', this.status);
    });

    this.robustSync.on('networkOnline', () => {
      this.status.phase = 'discovering';
      this.emit('statusChanged', this.status);
    });

    // Set up health monitor handlers
    this.healthMonitor.on('alertCreated', (alert) => {
      console.log(`[AutonomousSync] Health alert: ${alert.type} - ${alert.message}`);
      this.emit('healthAlert', alert);
    });

    this.healthMonitor.on('metricsCollected', (metrics) => {
      this.emit('healthMetrics', metrics);
    });

    // Set up conflict resolver handlers
    this.conflictResolver.on('conflictDetected', (conflict) => {
      console.log(`[AutonomousSync] Conflict detected: ${conflict.docId}`);
      this.emit('conflictDetected', conflict);
    });

    this.conflictResolver.on('conflictResolved', (conflict) => {
      console.log(`[AutonomousSync] Conflict resolved: ${conflict.docId}`);
      this.emit('conflictResolved', conflict);
    });
  }

  /**
   * Set up event handlers for error recovery system
   */
  private setupErrorRecoveryHandlers(): void {
    this.errorRecovery.on('recoveryScheduled', ({ attempt, delay }) => {
      console.log(`[AutonomousSync] Recovery scheduled for ${attempt.dbName}: attempt ${attempt.attempt} in ${delay}ms`);
      this.emit('recoveryScheduled', { attempt, delay });
    });

    this.errorRecovery.on('recoverySuccessful', (attempt) => {
      console.log(`[AutonomousSync] Recovery successful: ${attempt.dbName} on ${attempt.serverId}`);
      this.emit('recoverySuccessful', attempt);
    });

    this.errorRecovery.on('recoveryFailed', (attempt) => {
      console.error(`[AutonomousSync] Recovery failed: ${attempt.dbName} on ${attempt.serverId}`);
      this.emit('recoveryFailed', attempt);
    });

    this.errorRecovery.on('circuitBreakerOpened', (breaker) => {
      console.warn(`[AutonomousSync] Circuit breaker opened for ${breaker.serverId}`);
      this.emit('circuitBreakerOpened', breaker);
    });

    this.errorRecovery.on('circuitBreakerReset', ({ serverId }) => {
      console.log(`[AutonomousSync] Circuit breaker reset for ${serverId}`);
      this.emit('circuitBreakerReset', { serverId });
    });

    this.errorRecovery.on('restartDatabaseSync', async ({ serverId, dbName }) => {
      console.log(`[AutonomousSync] Restarting database sync: ${dbName} on ${serverId}`);
      // This would trigger a restart of the specific database sync
      // For now, we'll just emit the event
      this.emit('databaseSyncRestart', { serverId, dbName });
    });
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = undefined;
    }
    
    if (this.connectionHealthInterval) {
      clearInterval(this.connectionHealthInterval);
      this.connectionHealthInterval = undefined;
    }
    
    if (this.networkChangeTimeout) {
      clearTimeout(this.networkChangeTimeout);
      this.networkChangeTimeout = undefined;
    }
    
    // Remove network event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleNetworkChange.bind(this));
      window.removeEventListener('offline', this.handleNetworkChange.bind(this));
      
      if ('navigator' in window && 'connection' in navigator) {
        const connection = (navigator as any).connection;
        if (connection) {
          connection.removeEventListener('change', this.handleNetworkChange.bind(this));
        }
      }
    }

    // Stop robust sync components
    await this.robustSync.stopSync();
    this.healthMonitor.stopMonitoring();
    this.errorRecovery.stop();

    // Stop all active syncs via sync bridge (legacy)
    await this.syncBridge.stopAllConnections();

    this.status.phase = 'initializing';
    this.status.localPeers = [];
    this.status.activeSyncs = [];

    this.emit('statusChanged', this.status);
  }

  getStatus(): AutonomousSyncStatus {
    return { ...this.status };
  }

  /**
   * Get robust sync manager instance
   */
  getRobustSync(): RobustPouchDBSync {
    return this.robustSync;
  }

  /**
   * Get health monitor instance
   */
  getHealthMonitor(): SyncHealthMonitor {
    return this.healthMonitor;
  }

  /**
   * Get conflict resolver instance
   */
  getConflictResolver(): SyncConflictResolver {
    return this.conflictResolver;
  }

  /**
   * Get current sync health summary
   */
  getSyncHealthSummary() {
    return this.healthMonitor.getHealthSummary();
  }

  /**
   * Get robust sync status
   */
  getRobustSyncStatus(): DatabaseSyncState[] {
    return this.robustSync.getSyncStatus();
  }

  /**
   * Get server information
   */
  getServerInfo() {
    return this.robustSync.getServers();
  }

  /**
   * Get error recovery instance
   */
  getErrorRecovery(): SyncErrorRecovery {
    return this.errorRecovery;
  }

  /**
   * Get recovery statistics
   */
  getRecoveryStats() {
    return this.errorRecovery.getRecoveryStats();
  }

  /**
   * Get circuit breaker states
   */
  getCircuitBreakerStates() {
    return this.errorRecovery.getCircuitBreakerStates();
  }

  getConfig(): AutonomousSyncConfig {
    return { ...this.config };
  }

  private async startDiscovery(): Promise<void> {
    // This will be implemented by the discovery manager
    // For now, emit discovery started event
    this.emit('discoveryStarted');
    
    // Set up periodic discovery
    this.discoveryInterval = setInterval(async () => {
      if (this.isRunning) {
        await this.performDiscovery();
      }
    }, this.config.retryInterval);

    // Perform initial discovery
    await this.performDiscovery();
  }

  private async performDiscovery(): Promise<void> {
    try {
      this.status.phase = 'discovering';
      this.emit('statusChanged', this.status);
      
      console.log('[AutonomousSync] Starting HTTP discovery...');
      this.emit('discoveryAttempt', {
        timestamp: new Date(),
        method: 'http-scan',
        ports: this.config.knownPorts
      });
      
      // Use HTTP discovery manager to find CouchDB servers with restaurant validation
      const discoveryResult = await this.httpDiscovery.startDiscovery();
      
      console.log(`[AutonomousSync] Discovery completed: found ${discoveryResult.localPeers.length} peers (${discoveryResult.localPeers.filter(p => p.verified).length} verified)`);
      
      // Update status with discovery results
      this.status.localPeers = discoveryResult.localPeers;
      this.status.networkDiagnostics = discoveryResult.networkConditions;
      
      // Establish connections to discovered peers - prioritize verified peers
      if (discoveryResult.localPeers.length > 0) {
        console.log('[AutonomousSync] Establishing connections to discovered peers...');
        
        // Split verified and unverified peers
        const verifiedPeers = discoveryResult.localPeers.filter(p => p.verified);
        const unverifiedPeers = discoveryResult.localPeers.filter(p => !p.verified);
        
        if (verifiedPeers.length > 0) {
          console.log(`[AutonomousSync] Connecting to ${verifiedPeers.length} verified peers first`);
          
          // Connect to all verified peers simultaneously  
          const connectionPromises = verifiedPeers.map(peer => 
            this.establishConnection(peer, this.config.autoSyncDatabases)
          );
          const results = await Promise.allSettled(connectionPromises);
          
          const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length;
          console.log(`[AutonomousSync] Connected to ${successCount}/${verifiedPeers.length} verified peers`);
        }
        
        // Only connect to unverified peers if no verified peers succeeded and restaurant ID not configured
        if (this.status.activeSyncs.length === 0 && !this.config.restaurantId && unverifiedPeers.length > 0) {
          console.log(`[AutonomousSync] No verified peers available, connecting to ${unverifiedPeers.length} unverified peers`);
          
          for (const peer of unverifiedPeers) {
            await this.establishConnection(peer, this.config.autoSyncDatabases);
          }
        } else if (unverifiedPeers.length > 0) {
          console.log(`[AutonomousSync] Skipping ${unverifiedPeers.length} unverified peers (restaurant validation active)`);
        }
        
        this.emit('peersDiscovered', discoveryResult.localPeers);
      } else {
        console.log('[AutonomousSync] No peers found, will retry in', this.config.retryInterval, 'ms');
        
        // If no local peers found and internet fallback is enabled
        if (this.config.internetFallbackEnabled) {
          console.log('[AutonomousSync] Attempting internet fallback...');
          this.status.phase = 'fallback';
          this.emit('statusChanged', this.status);
          // TODO: Implement internet sync fallback
        }
      }
      
    } catch (error) {
      console.error('[AutonomousSync] Discovery failed:', error);
      this.status.lastError = error instanceof Error ? error.message : String(error);
      this.status.phase = 'error';
      this.emit('discoveryError', error);
      this.emit('statusChanged', this.status);
    }
  }

  private setupHealthMonitoring(): void {
    this.connectionHealthInterval = setInterval(() => {
      this.monitorConnectionHealth();
    }, 10000); // Check every 10 seconds
    
    // Set up network change detection for mobile devices  
    if (typeof window !== 'undefined' && 'navigator' in window && 'onLine' in navigator) {
      window.addEventListener('online', this.handleNetworkChange.bind(this));
      window.addEventListener('offline', this.handleNetworkChange.bind(this));
      
      // Listen for network interface changes (mobile WiFi switching)
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        if (connection) {
          connection.addEventListener('change', this.handleNetworkChange.bind(this));
        }
      }
    }
  }

  private async monitorConnectionHealth(): Promise<void> {
    // Monitor active connections and emit health updates
    for (const sync of this.status.activeSyncs) {
      // Check if connection is still alive by testing peer connectivity
      const peer = this.status.localPeers.find(p => p.id === sync.peerId);
      if (peer) {
        try {
          const headers: Record<string, string> = { 'Accept': 'application/json' };
          
          // Add auth if peer requires it
          if (peer.auth?.requiresAuth && peer.auth?.username && peer.auth?.password) {
            const credentials = btoa(`${peer.auth.username}:${peer.auth.password}`);
            headers['Authorization'] = `Basic ${credentials}`;
          }
          
          const response = await fetch(`http://${peer.ip}:${peer.port}`, {
            method: 'HEAD',
            headers,
            signal: AbortSignal.timeout(5000)
          });
          
          if (!response.ok) {
            console.log(`[AutonomousSync] Health check failed for peer ${peer.id}: ${response.status}`);
            
            // Mark sync as having connection issues
            sync.status = 'error';
            sync.errorCount += 1;
            
            // If too many errors, attempt reconnection
            if (sync.errorCount >= 3) {
              console.log(`[AutonomousSync] Too many errors for peer ${peer.id}, attempting reconnection...`);
              await this.handlePeerReconnection(peer);
            }
          } else {
            // Reset error count on successful health check
            if (sync.errorCount > 0) {
              console.log(`[AutonomousSync] Health check recovered for peer ${peer.id}`);
              sync.status = 'active';
              sync.errorCount = 0;
            }
          }
        } catch (error) {
          console.log(`[AutonomousSync] Health check error for peer ${peer.id}:`, error);
          sync.status = 'error';
          sync.errorCount += 1;
          
          if (sync.errorCount >= 3) {
            console.log(`[AutonomousSync] Peer ${peer.id} unreachable, attempting reconnection...`);
            await this.handlePeerReconnection(peer);
          }
        }
      }
      
      this.emit('connectionHealth', {
        peerId: sync.peerId,
        dbName: sync.dbName,
        status: sync.status,
        lastSync: sync.lastSync,
        errorCount: sync.errorCount
      });
    }
    
    // Emit updated status if any changes occurred
    this.emit('statusChanged', this.status);
  }
  
  private async handlePeerReconnection(peer: PeerInfo): Promise<void> {
    try {
      console.log(`[AutonomousSync] Attempting to reconnect to peer ${peer.id}`);
      
      // Remove existing connections for this peer
      this.status.activeSyncs = this.status.activeSyncs.filter(s => s.peerId !== peer.id);
      
      // Attempt to reestablish connection
      const success = await this.establishConnection(peer, this.config.autoSyncDatabases);
      
      if (success) {
        console.log(`[AutonomousSync] Successfully reconnected to peer ${peer.id}`);
        this.emit('connectionEstablished', peer);
      } else {
        console.log(`[AutonomousSync] Failed to reconnect to peer ${peer.id}`);
        this.emit('connectionLost', peer.id);
        
        // Remove peer from local peers list if reconnection fails
        this.status.localPeers = this.status.localPeers.filter(p => p.id !== peer.id);
      }
    } catch (error) {
      console.error(`[AutonomousSync] Error during peer reconnection for ${peer.id}:`, error);
      this.emit('connectionLost', peer.id);
    }
  }

  // Connection management methods
  async establishConnection(peer: PeerInfo, databases: string[]): Promise<boolean> {
    try {
      this.status.phase = 'connecting';
      this.emit('statusChanged', this.status);
      
      console.log(`[AutonomousSync] Establishing real PouchDB sync connections to ${peer.ip}:${peer.port}`);
      
      // Add peer to local peers if not already present
      if (!this.status.localPeers.find(p => p.id === peer.id)) {
        this.status.localPeers.push(peer);
      }

      // Add discovered server to robust sync manager
      this.robustSync.addServers([{
        ip: peer.ip,
        port: peer.port,
        serverInfo: { couchdb: true } // Assume CouchDB since we discovered it
      }]);

      // Start robust sync if not already running
      await this.robustSync.startSync();

      // Use the sync bridge to establish REAL PouchDB sync connections (legacy support)
      const bridgeConnections = await this.syncBridge.establishSyncConnection(peer, databases);

      // Convert bridge connections to daemon sync connections
      for (const bridgeConn of bridgeConnections) {
        const syncConnection: SyncConnection = {
          id: bridgeConn.id,
          peerId: bridgeConn.peerId,
          dbName: bridgeConn.dbName,
          status: bridgeConn.status as 'connecting' | 'active' | 'paused' | 'error',
          direction: bridgeConn.direction,
          lastSync: bridgeConn.lastSync,
          errorCount: bridgeConn.errorCount
        };

        this.status.activeSyncs.push(syncConnection);
        console.log(`[AutonomousSync] ✅ Real sync established: ${syncConnection.id}`);
      }

      this.status.phase = 'connected';
      this.status.connectionEstablishedTime = new Date();
      
      this.emit('connectionEstablished', peer);
      this.emit('statusChanged', this.status);
      
      console.log(`[AutonomousSync] ✅ Successfully established ${bridgeConnections.length} sync connections to ${peer.ip}:${peer.port}`);
      return true;
      
    } catch (error) {
      this.status.phase = 'error';
      this.status.lastError = error instanceof Error ? error.message : String(error);
      console.error(`[AutonomousSync] ❌ Failed to establish connection to ${peer.ip}:${peer.port}:`, error);
      this.emit('connectionError', { peer, error });
      this.emit('statusChanged', this.status);
      return false;
    }
  }

  async stopSync(peerId: string, dbName: string): Promise<void> {
    const syncIndex = this.status.activeSyncs.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (syncIndex >= 0) {
      this.status.activeSyncs.splice(syncIndex, 1);
      this.emit('syncStopped', { peerId, dbName });
      this.emit('statusChanged', this.status);
    }
  }

  // Event handler registration methods
  onConnectionEstablished(callback: (peer: PeerInfo) => void): void {
    this.on('connectionEstablished', callback);
  }

  onConnectionLost(callback: (peerId: string) => void): void {
    this.on('connectionLost', callback);
  }

  onStatusChanged(callback: (status: AutonomousSyncStatus) => void): void {
    this.on('statusChanged', callback);
  }

  onDiscoveryAttempt(callback: (attempt: any) => void): void {
    this.on('discoveryAttempt', callback);
  }

  onError(callback: (error: Error) => void): void {
    this.on('error', callback);
  }

  /**
   * Set up event handlers for the sync bridge
   */
  private setupSyncBridgeHandlers(): void {
    this.syncBridge.on('connectionEstablished', (connection: BridgeSyncConnection) => {
      console.log(`[AutonomousSync] Sync bridge connection established: ${connection.id}`);
      this.emit('syncConnectionEstablished', connection);
    });

    this.syncBridge.on('syncProgress', (data: { connection: BridgeSyncConnection; info: any }) => {
      // Update the corresponding sync connection in our status
      const syncIndex = this.status.activeSyncs.findIndex(s => s.id === data.connection.id);
      if (syncIndex >= 0) {
        this.status.activeSyncs[syncIndex].lastSync = data.connection.lastSync;
        this.status.activeSyncs[syncIndex].status = data.connection.status as any;
      }
      
      this.emit('syncProgress', data);
    });

    this.syncBridge.on('syncError', async (data: { connection: BridgeSyncConnection; error: any }) => {
      console.error(`[AutonomousSync] Sync error for ${data.connection.id}:`, data.error);
      
      // Update the corresponding sync connection in our status
      const syncIndex = this.status.activeSyncs.findIndex(s => s.id === data.connection.id);
      if (syncIndex >= 0) {
        this.status.activeSyncs[syncIndex].status = 'error';
        this.status.activeSyncs[syncIndex].errorCount = data.connection.errorCount;
        
        // Implement error recovery based on error type
        const errorMessage = data.error?.message || String(data.error);
        
        if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
          console.log(`[AutonomousSync] Authentication error for sync ${data.connection.id} - stopping sync`);
          await this.stopSync(data.connection.peerId, data.connection.dbName);
        } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
          console.log(`[AutonomousSync] Network error for sync ${data.connection.id} - will retry on next health check`);
          // Health monitoring will handle reconnection
        } else if (errorMessage.includes('conflict') || errorMessage.includes('revision')) {
          console.log(`[AutonomousSync] Document conflict for sync ${data.connection.id} - continuing sync`);
          // Document conflicts are normal and should be handled by PouchDB
          this.status.activeSyncs[syncIndex].status = 'active';
        } else {
          console.log(`[AutonomousSync] Unknown error for sync ${data.connection.id}: ${errorMessage}`);
          
          // For unknown errors, increment error count and retry if not too many
          if (this.status.activeSyncs[syncIndex].errorCount >= 5) {
            console.log(`[AutonomousSync] Too many errors for sync ${data.connection.id}, stopping sync`);
            await this.stopSync(data.connection.peerId, data.connection.dbName);
          }
        }
      }
      
      this.emit('syncError', data);
      this.emit('statusChanged', this.status);
    });

    this.syncBridge.on('syncPaused', (connection: BridgeSyncConnection) => {
      console.log(`[AutonomousSync] Sync paused: ${connection.id}`);
      
      // Update the corresponding sync connection in our status
      const syncIndex = this.status.activeSyncs.findIndex(s => s.id === connection.id);
      if (syncIndex >= 0) {
        this.status.activeSyncs[syncIndex].status = 'paused';
      }
      
      this.emit('syncPaused', connection);
    });

    this.syncBridge.on('syncResumed', (connection: BridgeSyncConnection) => {
      console.log(`[AutonomousSync] Sync resumed: ${connection.id}`);
      
      // Update the corresponding sync connection in our status
      const syncIndex = this.status.activeSyncs.findIndex(s => s.id === connection.id);
      if (syncIndex >= 0) {
        this.status.activeSyncs[syncIndex].status = 'active';
      }
      
      this.emit('syncResumed', connection);
    });

    this.syncBridge.on('connectionStopped', (connection: BridgeSyncConnection) => {
      console.log(`[AutonomousSync] Sync connection stopped: ${connection.id}`);
      
      // Remove the sync connection from our status
      const syncIndex = this.status.activeSyncs.findIndex(s => s.id === connection.id);
      if (syncIndex >= 0) {
        this.status.activeSyncs.splice(syncIndex, 1);
      }
      
      this.emit('syncConnectionStopped', connection);
      this.emit('statusChanged', this.status);
    });
  }

  getCachedPeers(): PeerInfo[] {
    return this.httpDiscovery.getCachedPeers();
  }

  clearDiscoveryCache(): void {
    this.httpDiscovery.clearCache();
  }

  async retryConnection(): Promise<boolean> {
    if (!this.isRunning) {
      return false;
    }
    
    console.log('[AutonomousSync] Retrying connection...');
    
    // Clear cache to force fresh discovery
    this.clearDiscoveryCache();
    
    // Perform fresh discovery
    await this.performDiscovery();
    
    return this.status.activeSyncs.length > 0;
  }
  
  private handleNetworkChange(): void {
    console.log('[AutonomousSync] Network change detected');
    
    if (!navigator.onLine) {
      console.log('[AutonomousSync] Device went offline');
      this.status.phase = 'error';
      this.status.lastError = 'Network disconnected';
      this.emit('statusChanged', this.status);
      return;
    }
    
    console.log('[AutonomousSync] Device came online or changed networks');
    
    // Debounce network changes to avoid rapid reconnection attempts
    if (this.networkChangeTimeout) {
      clearTimeout(this.networkChangeTimeout);
    }
    
    this.networkChangeTimeout = setTimeout(async () => {
      if (this.isRunning) {
        console.log('[AutonomousSync] Auto-retrying after network change...');
        await this.retryConnection();
      }
    }, 2000); // Wait 2 seconds before retrying
  }
  
}