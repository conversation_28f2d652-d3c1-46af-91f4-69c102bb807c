'use client';

import { useEffect, useRef } from 'react';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { isMobileApp } from '@/lib/utils/environment';

export function AutonomousSyncProvider({ children }: { children: React.ReactNode }) {
  const { db: mainDbInstance, isReady } = useUnifiedDB();
  const hasStarted = useRef(false);
  
  // Initialize autonomous sync with auto-start enabled
  const autonomousSync = useAutonomousSync(mainDbInstance, {
    enabled: true,
    autoSyncDatabases: [], // Will be determined dynamically based on restaurant ID
    discoveryTimeout: 60000, // 1 minute timeout
    retryDelay: 30000, // Retry every 30 seconds
    retryAttempts: 3
  });

  // Auto-start autonomous sync when app loads (mobile only)
  useEffect(() => {
    if (!isReady || !mainDbInstance || hasStarted.current) return;
    
    const isMobile = isMobileApp();
    
    // Only auto-start on mobile devices
    if (isMobile && autonomousSync.isInitialized && !autonomousSync.isAutonomousActive) {
      console.log('[AutonomousSyncProvider] Auto-starting autonomous sync for mobile device');
      hasStarted.current = true;
      
      // Start with a small delay to ensure everything is loaded
      setTimeout(() => {
        autonomousSync.startAutonomousSync();
      }, 2000);
    }
  }, [isReady, mainDbInstance, autonomousSync.isInitialized, autonomousSync.isAutonomousActive, autonomousSync.startAutonomousSync]);

  // Log sync status changes
  useEffect(() => {
    if (autonomousSync.autonomousState.phase) {
      console.log(`[AutonomousSyncProvider] Sync phase: ${autonomousSync.autonomousState.phase}`);
    }
  }, [autonomousSync.autonomousState.phase]);

  return <>{children}</>;
}