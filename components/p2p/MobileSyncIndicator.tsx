'use client';

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { 
  Wifi, 
  WifiOff, 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Activity,
  Zap
} from 'lucide-react';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';

interface MobileSyncIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export function MobileSyncIndicator({ 
  className = '', 
  showDetails = true 
}: MobileSyncIndicatorProps) {
  const {
    isInitialized,
    isAutonomousActive,
    autonomousState,
    robustSyncStatus,
    healthSummary,
    serverInfo,
    statusMessage
  } = useAutonomousSync(null);

  const [isOpen, setIsOpen] = useState(false);

  // Calculate overall sync health
  const getSyncHealth = () => {
    if (!isInitialized) return { status: 'initializing', color: 'bg-gray-500', icon: Activity };
    if (!isAutonomousActive) return { status: 'disconnected', color: 'bg-red-500', icon: WifiOff };
    
    if (healthSummary) {
      switch (healthSummary.overall) {
        case 'healthy':
          return { status: 'healthy', color: 'bg-green-500', icon: CheckCircle };
        case 'warning':
          return { status: 'warning', color: 'bg-yellow-500', icon: AlertTriangle };
        case 'critical':
          return { status: 'critical', color: 'bg-red-500', icon: XCircle };
      }
    }

    if (autonomousState.phase === 'connected') {
      return { status: 'connected', color: 'bg-green-500', icon: Wifi };
    }
    if (autonomousState.phase === 'discovering') {
      return { status: 'discovering', color: 'bg-blue-500', icon: Activity };
    }
    if (autonomousState.phase === 'connecting') {
      return { status: 'connecting', color: 'bg-yellow-500', icon: Activity };
    }
    
    return { status: 'error', color: 'bg-red-500', icon: XCircle };
  };

  const health = getSyncHealth();
  const Icon = health.icon;

  // Count active syncs
  const activeSyncs = robustSyncStatus ? robustSyncStatus.filter((db: any) => db.isActive).length : 0;
  const totalDatabases = robustSyncStatus ? robustSyncStatus.length : 0;

  // Count healthy servers
  const healthyServers = serverInfo ? serverInfo.filter((server: any) => server.isHealthy).length : 0;
  const totalServers = serverInfo ? serverInfo.length : 0;

  const formatTimestamp = (date: Date | string | undefined) => {
    if (!date) return 'Never';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleTimeString();
  };

  if (!showDetails) {
    // Simple indicator without popover
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className={`w-3 h-3 rounded-full ${health.color} animate-pulse`} />
        <span className="text-sm font-medium capitalize">{health.status}</span>
      </div>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className={`h-8 px-2 gap-2 ${className}`}
        >
          <div className={`w-2 h-2 rounded-full ${health.color} ${
            isAutonomousActive ? 'animate-pulse' : ''
          }`} />
          <Icon className="h-4 w-4" />
          <span className="text-sm capitalize">{health.status}</span>
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-4" align="end">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h3 className="font-semibold flex items-center gap-2">
              <Icon className="h-4 w-4" />
              Sync Status
            </h3>
            <Badge variant={isAutonomousActive ? 'default' : 'secondary'}>
              {autonomousState.phase}
            </Badge>
          </div>

          {/* Status Message */}
          <div className="text-sm text-muted-foreground">
            {statusMessage}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Databases:</span>
                <span className="font-medium">{activeSyncs}/{totalDatabases}</span>
              </div>
              <Progress 
                value={totalDatabases > 0 ? (activeSyncs / totalDatabases) * 100 : 0} 
                className="h-1" 
              />
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Servers:</span>
                <span className="font-medium">{healthyServers}/{totalServers}</span>
              </div>
              <Progress 
                value={totalServers > 0 ? (healthyServers / totalServers) * 100 : 0} 
                className="h-1" 
              />
            </div>
          </div>

          {/* Health Metrics */}
          {healthSummary?.metrics && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Sync Efficiency:</span>
                <span className="font-medium">
                  {healthSummary.metrics.syncEfficiency.toFixed(1)}%
                </span>
              </div>
              <Progress value={healthSummary.metrics.syncEfficiency} className="h-1" />
              
              <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                <div>
                  Docs: {healthSummary.metrics.totalDocsTransferred}
                </div>
                <div>
                  Conflicts: {healthSummary.metrics.totalConflicts}
                </div>
              </div>
            </div>
          )}

          {/* Active Alerts */}
          {healthSummary?.activeAlerts && healthSummary.activeAlerts.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                Active Alerts ({healthSummary.activeAlerts.length})
              </div>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {healthSummary.activeAlerts.slice(0, 3).map((alert: any) => (
                  <div key={alert.id} className="text-xs p-2 bg-muted rounded">
                    <div className="flex items-center gap-1">
                      {alert.type === 'error' ? (
                        <XCircle className="h-3 w-3 text-red-500" />
                      ) : (
                        <AlertTriangle className="h-3 w-3 text-yellow-500" />
                      )}
                      <span className="font-medium">{alert.message}</span>
                    </div>
                    <div className="text-muted-foreground mt-1">
                      {formatTimestamp(alert.timestamp)}
                    </div>
                  </div>
                ))}
                {healthSummary.activeAlerts.length > 3 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{healthSummary.activeAlerts.length - 3} more alerts
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Database Status */}
          {robustSyncStatus && robustSyncStatus.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium flex items-center gap-2">
                <Database className="h-4 w-4" />
                Databases
              </div>
              <div className="space-y-1 max-h-24 overflow-y-auto">
                {robustSyncStatus.map((db: any) => (
                  <div key={db.dbName} className="flex items-center justify-between text-xs">
                    <span className="font-medium">{db.dbName}</span>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        db.isActive ? 'bg-green-500' : 'bg-gray-400'
                      }`} />
                      <Badge variant={db.isActive ? 'default' : 'secondary'} className="text-xs">
                        {db.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Connection Info */}
          {autonomousState.connectionEstablishedTime && (
            <div className="text-xs text-muted-foreground border-t pt-2">
              Connected since: {formatTimestamp(autonomousState.connectionEstablishedTime)}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
