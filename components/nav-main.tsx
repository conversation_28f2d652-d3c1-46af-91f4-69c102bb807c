"use client"

import React from "react"
import { type LucideIcon } from "lucide-react"
import Link from "next/link"
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation"
import { useMobileLayout } from "@/hooks/use-mobile-layout"
import { cn } from "@/lib/utils"

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

export type NavItem = {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
};

export function NavMain({
  groupedItems,
  items,
}: {
  groupedItems?: NavItem[][];
  items?: NavItem[];
}) {
  const { navigate } = useStaticNavigation()
  const { isMobile } = useMobileLayout()

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault()
      const cleanPath = href.replace(/^\//, '')
      navigate(cleanPath)
    }
    // In dynamic mode, let <PERSON> handle it normally
  }

  // If groupedItems is provided, render with separators between groups
  if (groupedItems && groupedItems.length > 0) {
    return (
      <SidebarGroup>
        {groupedItems.map((group, idx) => (
          <React.Fragment key={"group-" + idx}>
            {idx > 0 && <div className="my-2 border-t border-muted" />}
            <SidebarMenu>
              {group.map((item) => (
                <React.Fragment key={item.title + "-" + idx}>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild tooltip={item.title}>
                      <Link
                        href={item.url}
                        onClick={(e) => handleNavClick(item.url, e)}
                        className={cn(
                          isMobile ? "min-h-[44px] text-foreground hover:bg-muted hover:text-foreground" : "",
                          item.isActive ? "bg-primary/10 text-primary font-medium" : "text-muted-foreground hover:text-foreground"
                        )}
                      >
                        {item.icon && <item.icon className="h-4 w-4" />}
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </React.Fragment>
              ))}
            </SidebarMenu>
          </React.Fragment>
          ))}
      </SidebarGroup>
    );  
  }
  // Fallback to single items prop for backward compatibility
  if (items && items.length > 0) {
    return (
      <SidebarGroup>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild tooltip={item.title}>
                <Link
                  href={item.url}
                  onClick={(e) => handleNavClick(item.url, e)}
                  className={cn(
                    isMobile ? "min-h-[44px] text-foreground hover:bg-muted hover:text-foreground" : "",
                    item.isActive ? "bg-primary/10 text-primary font-medium" : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  {item.icon && <item.icon className="h-4 w-4" />}
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>
    );
  }
  return null;
}
