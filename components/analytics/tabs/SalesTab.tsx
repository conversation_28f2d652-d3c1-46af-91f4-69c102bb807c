"use client"

import React, { useState, useEffect, useMemo } from 'react';
import { DateRange } from 'react-day-picker';
import { formatCurrency } from '@/lib/utils/currency';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart2Icon, 
  ShoppingCartIcon, 
  ClockIcon, 
  TruckIcon, 
  HomeIcon, 
  LogOutIcon, 
  TrendingUpIcon, 
  AreaChartIcon, 
  PackageIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  DollarSignIcon,
  PercentIcon,
  Loader2Icon
} from 'lucide-react';
import PeakHoursHeatmap from "@/components/analytics/PeakHoursHeatmap";
import { cn } from "@/lib/utils";
import { useOrderAnalyticsV4 } from '@/lib/hooks/useOrderAnalyticsV4';
import { Button } from "@/components/ui/button";
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation";
import { getAllStandardOrderTypes, getOrderTypeLabel, getOrderTypeUIConfig } from '@/lib/types/order-types';

// Define types for sale item (should match API response)
interface SaleItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  sales: number;
  unitPrice: number;
  profit: number;
  profitMargin: number;
  profitabilityScore?: number;
  contributionToTotalProfit?: number;
  [key: string]: string | number | undefined; 
}

// Define type for sales by order type (should match API response)
interface SalesByOrderType {
  type: string;
  orderCount: number;
  totalSales: number;
  avgTicket: number;
  percentage: number;
}

// Define type for peak hours (should match API response)
interface PeakHour {
  hour: string;
  sales: number;
  orders: number;
  avgTicket: number;
}

// Define type for KPIs (should match API response)
interface KPIs {
  netSales: number;
  orderCount: number;
  avgTicket: number;
  avgItemsPerOrder: number;
  totalProfit?: number;
  grossProfitMargin?: number;
}

// Define type for the raw order data used by the heatmap (if needed directly)
interface HeatmapOrderData {
  id: string;
  createdAt: string;
  status: string;
  total: number;
  orderType: string;
}

interface SalesTabData {
  salesByItem: SaleItem[];
  salesByOrderType: SalesByOrderType[];
  peakHours: PeakHour[];
  orders: HeatmapOrderData[];
  kpis: KPIs;
}

interface SalesTabProps {
  dateRange?: DateRange;
}

const getProfitabilityColor = (score?: number): string => {
  if (score === undefined || isNaN(score)) return "text-muted-foreground";
  if (score >= 70) return "text-green-600 dark:text-green-500";
  if (score >= 30) return "text-amber-600 dark:text-amber-500";
  return "text-red-600 dark:text-red-500";
};

export default function SalesTab({ dateRange }: SalesTabProps) {
  const [sortField, setSortField] = useState<string>("sales");
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [showProfit, setShowProfit] = useState<boolean>(true);
  
  // Use the client-side hook instead of fetch API
  const startDate = dateRange?.from?.toISOString();
  const endDate = dateRange?.to?.toISOString();
  const { data, isLoading, error } = useOrderAnalyticsV4(startDate, endDate);
  
  // Use useMemo for sorted sales data to avoid recalculating on every render
  const sortedSalesByItem = useMemo(() => {
    if (!data?.salesByItem) return [];
    return [...data.salesByItem].sort((a, b) => {
      const aValue = a[sortField as keyof typeof a] as number;
      const bValue = b[sortField as keyof typeof b] as number;
      
      if (sortDirection === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });
  }, [data?.salesByItem, sortField, sortDirection]);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const { navigate } = useStaticNavigation();

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault();
      const cleanPath = href.replace(/^\//, '');
      navigate(cleanPath);
    }
    // In dynamic mode, let the browser handle it normally
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2Icon className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-lg">Loading sales data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-red-600">
        <BarChart2Icon className="h-12 w-12 mb-2" />
        <p className="text-lg font-semibold">Error loading sales data</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <BarChart2Icon className="h-12 w-12 mb-2 text-muted-foreground" />
        <div className="text-center space-y-2">
          <p className="text-lg font-semibold">No sales data available</p>
          <p className="text-sm text-muted-foreground">
            No orders found for the selected period. 
          </p>
          <p className="text-xs text-muted-foreground">
            Create some orders first to see analytics data here.
          </p>
        </div>
        <div className="flex gap-2 mt-4">
          <Button variant="outline" size="sm" asChild>
            <a href="/ordering" onClick={(e) => handleNavClick("/ordering", e)}>🛒 Create Orders</a>
          </Button>
          <Button variant="outline" size="sm" asChild>
            <a href="/caisse" onClick={(e) => handleNavClick("/caisse", e)}>💳 Process Orders</a>
          </Button>
        </div>
      </div>
    );
  }

  const { salesByItem, salesByOrderType, peakHours, orders, kpis } = data;
  const { netSales = 0, orderCount = 0, avgTicket = 0, avgItemsPerOrder = 0, totalProfit = 0, grossProfitMargin = 0 } = kpis || {};

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="pt-5">
            <div className="flex flex-col space-y-1.5">
              <div className="flex items-center">
                <AreaChartIcon className="h-5 w-5 text-primary mr-2" />
                <p className="text-sm text-muted-foreground">Ventes Nettes</p>
              </div>
              <div className="text-2xl font-bold">{formatCurrency(netSales)}</div>
              <div className="text-xs text-muted-foreground">Période sélectionnée</div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="pt-5">
            <div className="flex flex-col space-y-1.5">
              <div className="flex items-center">
                <DollarSignIcon className="h-5 w-5 text-green-600 mr-2" />
                <p className="text-sm text-muted-foreground">Bénéfice Total</p>
              </div>
              <div className="text-2xl font-bold">{formatCurrency(totalProfit)}</div>
              <div className="text-xs text-muted-foreground">Période sélectionnée</div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="pt-5">
            <div className="flex flex-col space-y-1.5">
              <div className="flex items-center">
                <PercentIcon className="h-5 w-5 text-sky-600 mr-2" />
                <p className="text-sm text-muted-foreground">Marge Bénéf. Brute</p>
              </div>
              <div className="text-2xl font-bold">{grossProfitMargin.toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">Période sélectionnée</div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="pt-5">
            <div className="flex flex-col space-y-1.5">
              <div className="flex items-center">
                <PackageIcon className="h-5 w-5 text-primary mr-2" />
                <p className="text-sm text-muted-foreground">Commandes</p>
              </div>
              <div className="text-2xl font-bold">{orderCount}</div>
              <div className="text-xs text-muted-foreground">Période sélectionnée</div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="pt-5">
            <div className="flex flex-col space-y-1.5">
              <div className="flex items-center">
                <TrendingUpIcon className="h-5 w-5 text-primary mr-2" />
                <p className="text-sm text-muted-foreground">Ticket Moyen</p>
              </div>
              <div className="text-2xl font-bold">{formatCurrency(avgTicket)}</div>
              <div className="text-xs text-muted-foreground">Période sélectionnée</div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="pt-5">
            <div className="flex flex-col space-y-1.5">
              <div className="flex items-center">
                <ShoppingCartIcon className="h-5 w-5 text-primary mr-2" />
                <p className="text-sm text-muted-foreground">Articles par Commande</p>
              </div>
              <div className="text-2xl font-bold">{avgItemsPerOrder.toFixed(1)}</div>
              <div className="text-xs text-muted-foreground">Période sélectionnée</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Type Cards - always render, show empty state if no data */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
        {salesByOrderType && salesByOrderType.length > 0 ? (
          salesByOrderType.map((orderType, i) => (
            <Card key={i} className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-1 pt-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-semibold flex items-center gap-1.5">
                    {orderType.type === 'delivery' && <TruckIcon className="h-4 w-4 text-green-500" />}
                    {orderType.type === 'dine-in' && <HomeIcon className="h-4 w-4 text-blue-500" />}
                    {orderType.type === 'takeaway' && <LogOutIcon className="h-4 w-4 text-amber-500" />}
                    <span>
                      {getOrderTypeLabel(orderType.type)}
                    </span>
                  </CardTitle>
                  <Badge variant="outline" className="font-normal text-xs">
                      {orderType.percentage.toFixed(1)}%
                  </Badge>
                </div>
                <CardDescription className="pt-1 text-xs">
                    {orderType.orderCount} commandes
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2 pb-4">
                <Progress value={orderType.percentage} className="h-1.5 mb-2" />
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-xs text-muted-foreground">Ventes</p>
                    <p className="text-lg font-bold">{formatCurrency(orderType.totalSales)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Ticket Moyen</p>
                    <p className="text-lg font-bold">{formatCurrency(orderType.avgTicket)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          // Display placeholders for empty state
          getAllStandardOrderTypes().map(type => (
            <Card key={type} className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-1 pt-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-semibold flex items-center gap-1.5">
                    {type === 'dine-in' && <HomeIcon className="h-4 w-4 text-blue-500" />}
                    {type === 'takeaway' && <LogOutIcon className="h-4 w-4 text-amber-500" />}
                    {type === 'delivery' && <TruckIcon className="h-4 w-4 text-green-500" />}
                    <span>
                      {getOrderTypeLabel(type)}
                    </span>
                  </CardTitle>
                  <Badge variant="outline" className="font-normal text-xs">
                      0.0%
                  </Badge>
                </div>
                <CardDescription className="pt-1 text-xs">
                    0 commandes
                  </CardDescription>
              </CardHeader>
              <CardContent className="pt-2 pb-4">
                <Progress value={0} className="h-1.5 mb-2" />
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-xs text-muted-foreground">Ventes</p>
                    <p className="text-lg font-bold">{formatCurrency(0)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Ticket Moyen</p>
                    <p className="text-lg font-bold">{formatCurrency(0)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Sales By Item Table - always render, show empty state if no data */}
      <Card className="shadow-sm hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <ShoppingCartIcon className="h-5 w-5 text-primary" />
                Détail des Ventes par Article
              </CardTitle>
              <CardDescription>
                  Performance de chaque article du menu {sortedSalesByItem && sortedSalesByItem.length === 0 && "(Aucune donnée disponible)"}
              </CardDescription>
            </div>
            
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
              <Select value={sortField} onValueChange={setSortField}>
                <SelectTrigger className="w-full sm:w-44">
                  <SelectValue placeholder="Trier par" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">Ventes (DZD)</SelectItem>
                  <SelectItem value="quantity">Quantité</SelectItem>
                  {showProfit && <SelectItem value="profit">Bénéfice</SelectItem>}
                  {showProfit && <SelectItem value="profitMargin">Marge %</SelectItem>}
                  {showProfit && <SelectItem value="profitabilityScore">Score Rentabilité</SelectItem>}
                    {showProfit && <SelectItem value="contributionToTotalProfit">% Contrib. Bénéfice</SelectItem>}
                </SelectContent>
              </Select>
              
              <Select value={sortDirection} onValueChange={(value) => setSortDirection(value as 'asc' | 'desc')}>
                <SelectTrigger className="w-full sm:w-36">
                  <SelectValue placeholder="Direction" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Décroissant</SelectItem>
                  <SelectItem value="asc">Croissant</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* Horizontally scrollable table layout */}
          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead onClick={() => handleSort('name')} className="cursor-pointer hover:bg-muted/50 min-w-[120px] text-xs sm:text-sm py-2 sm:py-3">
                    Nom {sortField === 'name' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>
                  <TableHead onClick={() => handleSort('category')} className="cursor-pointer hover:bg-muted/50 min-w-[100px] text-xs sm:text-sm py-2 sm:py-3">
                    Catégorie {sortField === 'category' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>
                  <TableHead onClick={() => handleSort('quantity')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[80px] text-xs sm:text-sm py-2 sm:py-3">
                    Qté {sortField === 'quantity' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>
                  <TableHead onClick={() => handleSort('sales')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[100px] text-xs sm:text-sm py-2 sm:py-3">
                    Ventes {sortField === 'sales' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>
                  {showProfit && <TableHead onClick={() => handleSort('unitPrice')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[90px] text-xs sm:text-sm py-2 sm:py-3">
                    Prix Unit. {sortField === 'unitPrice' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>}
                  {showProfit && <TableHead onClick={() => handleSort('profit')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[90px] text-xs sm:text-sm py-2 sm:py-3">
                    Bénéfice {sortField === 'profit' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>}
                  {showProfit && <TableHead onClick={() => handleSort('profitMargin')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[80px] text-xs sm:text-sm py-2 sm:py-3">
                    Marge % {sortField === 'profitMargin' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>}
                  {showProfit && <TableHead onClick={() => handleSort('profitabilityScore')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[90px] text-xs sm:text-sm py-2 sm:py-3">
                    Score {sortField === 'profitabilityScore' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>}
                  {showProfit && <TableHead onClick={() => handleSort('contributionToTotalProfit')} className="text-right cursor-pointer hover:bg-muted/50 min-w-[120px] text-xs sm:text-sm py-2 sm:py-3">
                    % Contrib. {sortField === 'contributionToTotalProfit' && (sortDirection === 'asc' ? <ArrowUpIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" /> : <ArrowDownIcon className="inline h-2.5 w-2.5 sm:h-3 sm:w-3 ml-1" />)}
                  </TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedSalesByItem && sortedSalesByItem.length > 0 ? (
                  sortedSalesByItem.map((item) => (
                    <TableRow key={item.id} className="hover:bg-muted/5">
                      <TableCell className="font-medium text-xs sm:text-sm py-2 sm:py-3">
                        {item.name}
                      </TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 sm:py-3">{item.category}</TableCell>
                      <TableCell className="text-right text-xs sm:text-sm py-2 sm:py-3">{item.quantity}</TableCell>
                      <TableCell className="text-right font-medium text-xs sm:text-sm py-2 sm:py-3">{formatCurrency(item.sales)}</TableCell>
                      {showProfit && <TableCell className="text-right text-xs sm:text-sm py-2 sm:py-3">{formatCurrency(item.unitPrice)}</TableCell>}
                      {showProfit && <TableCell className="text-right font-medium text-xs sm:text-sm py-2 sm:py-3">{formatCurrency(item.profit)}</TableCell>}
                      {showProfit && <TableCell className="text-right text-xs sm:text-sm py-2 sm:py-3">{item.profitMargin.toFixed(1)}%</TableCell>}
                      {showProfit && (
                        <TableCell className={cn("text-right font-semibold text-xs sm:text-sm py-2 sm:py-3", getProfitabilityColor(item.profitabilityScore))}>
                            {item.profitabilityScore?.toFixed(1) ?? 'N/A'}
                        </TableCell>
                      )}
                      {showProfit && (
                        <TableCell className="text-right text-xs sm:text-sm py-2 sm:py-3">
                          <div className="flex items-center justify-end space-x-2">
                              <span className="text-xs sm:text-sm">{item.contributionToTotalProfit?.toFixed(1) ?? 'N/A'}%</span>
                              <Progress value={item.contributionToTotalProfit || 0} className="w-8 sm:w-12 h-1.5" />
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={showProfit ? 9 : 4} className="h-20 sm:h-24 text-center text-muted-foreground py-8">
                      <div className="flex flex-col items-center gap-2">
                        <ShoppingCartIcon className="h-6 w-6 sm:h-8 sm:w-8 opacity-50" />
                        <p className="text-xs sm:text-sm">Aucune donnée disponible</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Peak Hours Chart - always render, even with empty data */}
      <div className="mt-6">
        {orders && orders.length > 0 ? (
          <PeakHoursHeatmap orders={orders} className="shadow-sm hover:shadow-md transition-shadow" />
        ) : (
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <ClockIcon className="h-5 w-5 text-primary" />
                Heures de Pointe
              </CardTitle>
              <CardDescription>
                Analyse de l'achalandage par heure (Aucune donnée disponible)
              </CardDescription>
            </CardHeader>
            <CardContent className="h-48 flex items-center justify-center text-muted-foreground">
              Aucune donnée d'heures de pointe disponible
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
} 