"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  TestTube, 
  Bug, 
  Printer, 
  MonitorSmartphone, 
  Settings,
  ExternalLink,
  Network,
  Wifi
} from "lucide-react";
import { useRouter } from "next/navigation";
import { P2PDebugInterface } from "./P2PDebugInterface";

export default function FloatingDebugButton() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isP2PDebugOpen, setIsP2PDebugOpen] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const debugRoutes = [
    {
      label: "🌐 P2P Sync Debug",
      description: "Real-time P2P sync troubleshooting & monitoring",
      action: () => setIsP2PDebugOpen(true),
      icon: <Network className="h-4 w-4" />,
      color: "text-cyan-600",
      isModal: true
    },
    {
      label: "🧪 Kitchen Printer Test",
      description: "Comprehensive printer testing suite",
      href: "/debug/kitchen-printer-test",
      icon: <TestTube className="h-4 w-4" />,
      color: "text-blue-600"
    },
    {
      label: "💰 Cash System Test",
      description: "Test cash register functionality",
      href: "/debug/caisse-test",
      icon: <MonitorSmartphone className="h-4 w-4" />,
      color: "text-green-600"
    },
    {
      label: "🔧 General Debug Tools",
      description: "Database cleanup and utilities",
      href: "/debug",
      icon: <Bug className="h-4 w-4" />,
      color: "text-purple-600"
    },
    {
      label: "⚙️ Settings",
      description: "Access all debug tools from settings",
      href: "/settings",
      icon: <Settings className="h-4 w-4" />,
      color: "text-gray-600"
    }
  ];

  const handleNavigation = (href: string) => {
    router.push(href);
    setIsOpen(false);
  };

  const handleAction = (route: any) => {
    if (route.isModal && route.action) {
      route.action();
      setIsOpen(false);
    } else if (route.href) {
      handleNavigation(route.href);
    }
  };

  return (
    <>
      <div className="fixed bottom-4 right-4 z-50">
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              size="sm"
              className="rounded-full shadow-lg bg-orange-600 hover:bg-orange-700 text-white border-2 border-orange-500"
            >
              <Bug className="h-4 w-4 mr-1" />
              Debug
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent 
            align="end" 
            className="w-80 p-2"
            side="top"
          >
            <DropdownMenuLabel className="flex items-center gap-2 text-orange-600">
              <TestTube className="h-4 w-4" />
              Development Debug Tools
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {debugRoutes.map((route, index) => (
              <DropdownMenuItem
                key={index}
                onClick={() => handleAction(route)}
                className="flex flex-col items-start p-3 cursor-pointer hover:bg-muted/50"
              >
                <div className="flex items-center gap-2 w-full">
                  <span className={route.color}>{route.icon}</span>
                  <span className="font-medium">{route.label}</span>
                  {route.isModal ? (
                    <Wifi className="h-3 w-3 ml-auto text-muted-foreground" />
                  ) : (
                    <ExternalLink className="h-3 w-3 ml-auto text-muted-foreground" />
                  )}
                </div>
                <span className="text-xs text-muted-foreground mt-1 ml-6">
                  {route.description}
                </span>
              </DropdownMenuItem>
            ))}
            
            <DropdownMenuSeparator />
            <div className="px-3 py-2 text-xs text-muted-foreground">
              💡 These tools are only available in development mode
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* P2P Debug Modal */}
      <Dialog open={isP2PDebugOpen} onOpenChange={setIsP2PDebugOpen}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              P2P Sync Debug Interface
            </DialogTitle>
          </DialogHeader>
          <P2PDebugInterface />
        </DialogContent>
      </Dialog>
    </>
  );
}
