'use client';

import React, { useState, useEffect } from 'react';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';
import { DiscoveredDevice } from '@/lib/services/simple-ip-discovery';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { SyncTroubleshooter } from './SyncTroubleshooter';
// Standalone IP discovery debugger - no database dependencies
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Server, 
  Database, 
  Network, 
  RefreshCw,
  Play,
  Square,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Wifi,
  Monitor,
  Phone,
  Zap,
  Search,
  Router,
  Globe,
  Signal,
  Gauge,
  MapPin,
  Shield,
  Timer,
  Eye,
  TrendingUp,
  Circle,
  AlertTriangle,
  RotateCcw,
  HardDrive,
  Cloud,
  FileText,
  Users,
  Package,
  Settings,
  BarChart3,
  Layers,
  Link,
  Unlink,
  TestTube,
  Smartphone
} from 'lucide-react';

interface SyncTestResult {
  status: string;
  server: string;
  timestamp: Date;
  steps: string[];
  message?: string;
  dbName?: string;
  dbInfo?: any;
  replicationInfo?: any;
  error?: string;
  serverUrl?: string;
  result?: any;
}

export function AutonomousSyncDebugger() {
  const { db: mainDbInstance, isReady } = useUnifiedDB();
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [customIP, setCustomIP] = useState('');
  const [customPort, setCustomPort] = useState('5984');
  const [selectedTab, setSelectedTab] = useState('discovery');
  const [syncDiagnostics, setSyncDiagnostics] = useState<any>(null);
  const [lastSyncTest, setLastSyncTest] = useState<SyncTestResult | null>(null);
  const [syncBridgeDebugInfo, setSyncBridgeDebugInfo] = useState<any>(null);
  const [authDataStatus, setAuthDataStatus] = useState<any>(null);
  // Standalone IP discovery debugger - no database dependencies
  
  const autonomousSync = useAutonomousSync(null, {
    enabled: false // Don't auto-start - user controlled
  });

  // Load sync bridge debug info
  const loadSyncBridgeDebugInfo = async () => {
    try {
      // Get debug info from localStorage
      const debugInfo = localStorage.getItem('pouchdb_sync_debug');
      if (debugInfo) {
        setSyncBridgeDebugInfo(JSON.parse(debugInfo));
      }

      // Get auth data status
      const authData = localStorage.getItem('auth_data');
      if (authData) {
        try {
          const parsed = JSON.parse(authData);
          setAuthDataStatus({
            exists: true,
            hasRestaurantId: !!parsed.restaurantId,
            restaurantId: parsed.restaurantId ? `${parsed.restaurantId.substring(0, 10)}...` : null,
            keys: Object.keys(parsed),
            valid: true
          });
        } catch (error) {
          setAuthDataStatus({
            exists: true,
            valid: false,
            error: 'Failed to parse JSON'
          });
        }
      } else {
        setAuthDataStatus({
          exists: false,
          valid: false
        });
      }
    } catch (error) {
      console.error('Error loading sync bridge debug info:', error);
    }
  };

  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
    
    // Load debug info on mount
    loadSyncBridgeDebugInfo();
    
    // Refresh debug info every 5 seconds
    const interval = setInterval(loadSyncBridgeDebugInfo, 5000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (phase: string) => {
    switch (phase) {
      case 'discovering':
      case 'connecting':
        return 'text-blue-500';
      case 'connected':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusIcon = (phase: string) => {
    switch (phase) {
      case 'discovering':
        return <Search className="h-5 w-5" />;
      case 'connecting':
        return <Network className="h-5 w-5" />;
      case 'connected':
        return <CheckCircle className="h-5 w-5" />;
      case 'error':
        return <XCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getBadgeVariant = (status: boolean): "default" | "secondary" | "destructive" | "outline" => {
    return status ? "default" : "secondary";
  };

  const discoveryStats = autonomousSync.getDiscoveryStats();
  const networkDiagnostics = autonomousSync.getNetworkDiagnostics();

  const getScanningProgress = () => {
    const totalAttempts = discoveryStats.totalAttempts;
    const expectedTotal = 254;
    return totalAttempts > 0 ? Math.min((totalAttempts / expectedTotal) * 100, 100) : 0;
  };

  // Diagnostic functions
  const testDatabaseConnection = async (serverUrl: string, dbName: string) => {
    try {
      const response = await fetch(`${serverUrl}/${dbName}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const dbInfo = await response.json();
        return { success: true, info: dbInfo };
      } else {
        return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  };

  const runSyncDiagnostics = async () => {
    const couchDBServers = autonomousSync.discoveredDevices.filter(device =>
      device.serverInfo?.couchdb || (device.port >= 5984 && device.port <= 5987)
    );

    if (couchDBServers.length === 0) {
      setSyncDiagnostics({
        status: 'no_servers',
        message: 'No CouchDB servers found. Run discovery first.',
        timestamp: new Date()
      });
      return;
    }

    const diagnostics = {
      status: 'running',
      timestamp: new Date(),
      servers: [],
      databases: ['orders', 'staff', 'inventory', 'settings'],
      results: {}
    };

    setSyncDiagnostics(diagnostics);

    for (const server of couchDBServers) {
      const serverUrl = `http://${server.ip}:${server.port}`;
      const serverDiag = {
        id: `${server.ip}:${server.port}`,
        url: serverUrl,
        reachable: false,
        databases: {},
        error: null
      };

      try {
        // Test server connectivity
        const serverResponse = await fetch(serverUrl);
        if (serverResponse.ok) {
          serverDiag.reachable = true;

          // Test each database
          for (const dbName of diagnostics.databases) {
            const dbTest = await testDatabaseConnection(serverUrl, dbName);
            serverDiag.databases[dbName] = dbTest;
          }
        } else {
          serverDiag.error = `Server unreachable: HTTP ${serverResponse.status}`;
        }
      } catch (error) {
        serverDiag.error = `Connection failed: ${(error as Error).message}`;
      }

      diagnostics.servers.push(serverDiag);
    }

    diagnostics.status = 'completed';
    setSyncDiagnostics(diagnostics);
  };

  const testSyncConnection = async (serverUrl: string, dbName: string) => {
    try {
      // Test if we can create a test document
      const testDoc = {
        _id: `sync-test-${Date.now()}`,
        type: 'sync-test',
        timestamp: new Date().toISOString(),
        platform: platform
      };

      const response = await fetch(`${serverUrl}/${dbName}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testDoc)
      });

      if (response.ok) {
        const result = await response.json();

        // Try to delete the test document
        try {
          await fetch(`${serverUrl}/${dbName}/${testDoc._id}?rev=${result.rev}`, {
            method: 'DELETE'
          });
        } catch (deleteError) {
          console.warn('Failed to cleanup test document:', deleteError);
        }

        return {
          success: true,
          message: 'Sync test successful - can read/write to database',
          details: result
        };
      } else {
        return {
          success: false,
          error: `Failed to write test document: HTTP ${response.status}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Sync test failed: ${(error as Error).message}`
      };
    }
  };

  const formatTimestamp = (date: Date | string | undefined) => {
    if (!date) return 'Never';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleString();
  };

  const getDatabaseIcon = (dbName: string) => {
    switch (dbName) {
      case 'orders': return <FileText className="h-4 w-4" />;
      case 'staff': return <Users className="h-4 w-4" />;
      case 'inventory': return <Package className="h-4 w-4" />;
      case 'settings': return <Settings className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6 p-4 md:p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="border-b pb-4">
        <h1 className="text-2xl md:text-3xl font-bold">P2P Sync Debug Console</h1>
        <p className="text-muted-foreground mt-2">
          Comprehensive debugging for network discovery and database synchronization
        </p>
        <p className="text-sm text-muted-foreground mt-1">
          🚀 Network Discovery | 🔄 Database Sync | 📊 Real-time Monitoring | 🔧 Diagnostics
        </p>
        {autonomousSync.discoveredDevices.some(d => d.serverInfo?.couchdb || (d.port >= 5984 && d.port <= 5987)) ? (
          <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-800">
              ✅ CouchDB servers found - sync diagnostics available
            </p>
          </div>
        ) : (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              🔄 Auto-scanning active - will continuously scan until CouchDB servers are found
            </p>
          </div>
        )}
      </div>

      {/* Main Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="discovery">Discovery</TabsTrigger>
          <TabsTrigger value="sync">Database Sync</TabsTrigger>
          <TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
          <TabsTrigger value="sync-debug">Sync Debug</TabsTrigger>
          <TabsTrigger value="troubleshooter">Troubleshooter</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="discovery" className="space-y-6 mt-6">

      {/* Main Status Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-3">
              <div className={`p-2 rounded-full bg-muted ${getStatusColor(autonomousSync.autonomousState.phase)}`}>
                {getStatusIcon(autonomousSync.autonomousState.phase)}
              </div>
              <div>
                <div className="text-xl">IP Discovery Status</div>
                <div className="text-sm text-muted-foreground font-normal">
                  {autonomousSync.statusMessage}
                </div>
              </div>
            </CardTitle>
            <div className="flex space-x-2">
              <Button
                variant={autonomousSync.isAutonomousActive ? "destructive" : "default"}
                size="sm"
                onClick={autonomousSync.isAutonomousActive ? autonomousSync.stopAutonomousSync : autonomousSync.startAutonomousSync}
                disabled={!autonomousSync.isInitialized}
              >
                {autonomousSync.isAutonomousActive ? (
                  <>
                    <Square className="h-4 w-4 mr-2" />
                    Stop Scan
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Start Scan
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={autonomousSync.retryConnection}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status Badges */}
            <div className="flex flex-wrap gap-2">
              <Badge variant={getBadgeVariant(autonomousSync.isAutonomousActive)}>
                {autonomousSync.isAutonomousActive ? 'Scanning' : 'Stopped'}
              </Badge>
              <Badge variant={getBadgeVariant(autonomousSync.isInitialized)}>
                {autonomousSync.isInitialized ? 'Ready' : 'Not Ready'}
              </Badge>
              <Badge variant={getBadgeVariant(autonomousSync.discoveredDevices.length > 0)}>
                {autonomousSync.discoveredDevices.length} Devices Found
              </Badge>
              <Badge variant={getBadgeVariant(autonomousSync.discoveredPeers.length > 0)}>
                {autonomousSync.discoveredPeers.length} HTTP Servers
              </Badge>
              <Badge variant="outline">
                {platform} Platform
              </Badge>
            </div>

            {/* Scanning Progress */}
            {autonomousSync.autonomousState.phase === 'discovering' && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Network Scan Progress</span>
                  <span className="text-sm text-muted-foreground">
                    {discoveryStats.totalAttempts}/254 IPs ({Math.round(getScanningProgress())}%)
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getScanningProgress()}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Found: {discoveryStats.successfulConnections} devices</span>
                  <span>Avg: {Math.round(discoveryStats.averageResponseTime)}ms</span>
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{autonomousSync.discoveredDevices.length}</div>
                <div className="text-sm text-muted-foreground">Devices Found</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-green-600">{autonomousSync.discoveredDevices.filter(d => d.isVerified).length}</div>
                <div className="text-sm text-muted-foreground">Verified</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{discoveryStats.totalAttempts}</div>
                <div className="text-sm text-muted-foreground">IPs Scanned</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{Math.round(discoveryStats.averageResponseTime)}</div>
                <div className="text-sm text-muted-foreground">Avg Response (ms)</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform & Manual Testing */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {isMobile ? <Phone className="h-5 w-5" /> : <Monitor className="h-5 w-5" />}
              <span>Platform Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Platform:</span>
                <Badge variant="outline">{platform}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Mobile App:</span>
                <Badge variant={getBadgeVariant(isMobile)}>{isMobile ? 'Yes' : 'No'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Desktop App:</span>
                <Badge variant={getBadgeVariant(isDesktop)}>{isDesktop ? 'Yes' : 'No'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Scanner Ready:</span>
                <Badge variant={getBadgeVariant(autonomousSync.isInitialized)}>{autonomousSync.isInitialized ? 'Ready' : 'Not Ready'}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Network className="h-5 w-5" />
              <span>Manual Testing</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="text"
                  placeholder="IP Address"
                  value={customIP}
                  onChange={(e) => setCustomIP(e.target.value)}
                  className="px-3 py-2 border rounded-md text-sm"
                />
                <input
                  type="text"
                  placeholder="Port"
                  value={customPort}
                  onChange={(e) => setCustomPort(e.target.value)}
                  className="px-3 py-2 border rounded-md text-sm"
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => autonomousSync.testSpecificIP(customIP, parseInt(customPort))}
                  disabled={!customIP || !customPort}
                  className="w-full"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Test IP
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => autonomousSync.quickScan('192.168.1')}
                  className="w-full"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Quick Scan
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={autonomousSync.testCleartextHttp}
                className="w-full"
              >
                <Globe className="h-4 w-4 mr-2" />
                Test HTTP
              </Button>
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Simple HTTP scanner - no database required. Scans ports 5984-5987 on common subnets.
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discovery & Network Diagnostics */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Discovery Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Live Scanning Progress */}
              {autonomousSync.autonomousState.phase === 'discovering' && (
                <div className="p-4 bg-blue-50 rounded-lg space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin">
                      <Search className="h-4 w-4 text-blue-600" />
                    </div>
                    <span className="font-medium text-blue-800">Live Network Scanning</span>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Scanning Progress</span>
                      <span className="font-mono">{discoveryStats.totalAttempts}/254 IPs ({Math.round(getScanningProgress())}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${getScanningProgress()}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="text-lg font-bold text-blue-600">{discoveryStats.totalAttempts}</div>
                      <div className="text-xs text-blue-700">Scanned</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="text-lg font-bold text-green-600">{discoveryStats.successfulConnections}</div>
                      <div className="text-xs text-green-700">Found</div>
                    </div>
                    <div className="text-center p-2 bg-purple-50 rounded">
                      <div className="text-lg font-bold text-purple-600">{Math.round(discoveryStats.averageResponseTime)}</div>
                      <div className="text-xs text-purple-700">Avg ms</div>
                    </div>
                    <div className="text-center p-2 bg-orange-50 rounded">
                      <div className="text-lg font-bold text-orange-600">{Math.round((discoveryStats.successfulConnections / Math.max(discoveryStats.totalAttempts, 1)) * 100)}</div>
                      <div className="text-xs text-orange-700">Hit Rate %</div>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Total Attempts</div>
                  <div className="text-2xl font-bold">{discoveryStats.totalAttempts}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Successful</div>
                  <div className="text-2xl font-bold text-green-600">{discoveryStats.successfulConnections}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Avg Response</div>
                  <div className="text-2xl font-bold text-blue-600">{Math.round(discoveryStats.averageResponseTime)}ms</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Active Devices</div>
                  <div className="text-2xl font-bold text-purple-600">{discoveryStats.activeDevices}</div>
                </div>
              </div>

              {discoveryStats.lastDiscoveryTime && (
                <div className="text-sm text-muted-foreground">
                  Last discovery: {discoveryStats.lastDiscoveryTime.toLocaleString()}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Network className="h-5 w-5" />
              <span>Network Diagnostics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Local IP</div>
                  <div className="font-mono text-sm bg-muted p-2 rounded">
                    {networkDiagnostics.localIP || 'Detecting...'}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Subnet</div>
                  <div className="font-mono text-sm bg-muted p-2 rounded">
                    {networkDiagnostics.subnet || 'Detecting...'}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Scanned IPs</div>
                  <div className="text-2xl font-bold">{networkDiagnostics.scannedIPs}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Responsive</div>
                  <div className="text-2xl font-bold text-green-600">{networkDiagnostics.responsiveIPs}</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium">Network Health</div>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min((networkDiagnostics.responsiveIPs / Math.max(networkDiagnostics.scannedIPs, 1)) * 100, 100)}%` 
                      }}
                    />
                  </div>
                  <span className="text-sm font-mono">
                    {Math.round((networkDiagnostics.responsiveIPs / Math.max(networkDiagnostics.scannedIPs, 1)) * 100)}%
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <Button variant="outline" size="sm" onClick={autonomousSync.testCleartextHttp} className="w-full">
                  <Globe className="h-4 w-4 mr-2" />
                  Test Cleartext HTTP
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discovered Devices */}
      {autonomousSync.discoveredDevices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Server className="h-5 w-5" />
              <span>Discovered Devices ({autonomousSync.discoveredDevices.length})</span>
            </CardTitle>
            <CardDescription>
              HTTP servers found on your local network
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {autonomousSync.discoveredDevices.map((device) => (
                <div key={`${device.ip}:${device.port}`} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Database className="h-5 w-5 text-blue-500" />
                      <span className="font-medium truncate">{device.hostname}</span>
                    </div>
                    {device.isVerified && (
                      <Badge variant="default" className="text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verified
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">IP:</span>
                      <span className="font-mono">{device.ip}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Port:</span>
                      <span className="font-mono">{device.port}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Response:</span>
                      <span className="font-mono text-xs">{device.responseTime}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Seen:</span>
                      <span className="font-mono text-xs">{device.lastSeen.toLocaleTimeString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Badge variant="default" className="text-xs">
                      <Wifi className="h-3 w-3 mr-1" />
                      Online
                    </Badge>
                    {device.serverInfo?.couchdb && (
                      <Badge variant="outline" className="text-xs">
                        <Database className="h-3 w-3 mr-1" />
                        CouchDB
                      </Badge>
                    )}
                  </div>
                  
                  {device.serverInfo && (
                    <div className="text-xs text-muted-foreground">
                      <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(device.serverInfo, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activity Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Activity Logs</span>
              <Badge variant="outline">{autonomousSync.logs.length}</Badge>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={() => autonomousSync.logs.splice(0)}>
                <XCircle className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Real-time discovery and sync activity with detailed diagnostics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {autonomousSync.logs.length === 0 ? (
              <div className="text-center py-12">
                <Activity className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
                <p className="text-lg font-medium text-muted-foreground">No activity logged yet</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Logs will appear here when autonomous sync starts running
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {autonomousSync.logs.slice(0, 50).map((log, index) => {
                  // Parse log for better formatting
                  const getLogStyle = (logText: string) => {
                    if (logText.includes('✅') || logText.includes('success')) {
                      return 'border-l-green-500 bg-green-50 text-green-800';
                    } else if (logText.includes('❌') || logText.includes('Error') || logText.includes('failed')) {
                      return 'border-l-red-500 bg-red-50 text-red-800';
                    } else if (logText.includes('⚠️') || logText.includes('warn')) {
                      return 'border-l-yellow-500 bg-yellow-50 text-yellow-800';
                    } else if (logText.includes('🔍') || logText.includes('Starting') || logText.includes('Discovery')) {
                      return 'border-l-blue-500 bg-blue-50 text-blue-800';
                    } else if (logText.includes('🎯') || logText.includes('found') || logText.includes('Connected')) {
                      return 'border-l-purple-500 bg-purple-50 text-purple-800';
                    }
                    return 'border-l-gray-500 bg-gray-50 text-gray-800';
                  };

                  const getLogIcon = (logText: string) => {
                    if (logText.includes('✅') || logText.includes('success')) {
                      return <CheckCircle className="h-4 w-4 text-green-500" />;
                    } else if (logText.includes('❌') || logText.includes('Error') || logText.includes('failed')) {
                      return <XCircle className="h-4 w-4 text-red-500" />;
                    } else if (logText.includes('⚠️') || logText.includes('warn')) {
                      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
                    } else if (logText.includes('🔍') || logText.includes('Starting') || logText.includes('Discovery')) {
                      return <Search className="h-4 w-4 text-blue-500" />;
                    } else if (logText.includes('🎯') || logText.includes('found') || logText.includes('Connected')) {
                      return <Zap className="h-4 w-4 text-purple-500" />;
                    }
                    return <Activity className="h-4 w-4 text-gray-500" />;
                  };

                  // Extract timestamp and message
                  const parts = log.split(' - ');
                  const timestamp = parts[0];
                  const message = parts.slice(1).join(' - ');

                  return (
                    <div key={index} className={`border-l-4 p-3 rounded-r ${getLogStyle(log)}`}>
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getLogIcon(log)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-mono break-words">
                            {message || log}
                          </div>
                          <div className="text-xs opacity-75 mt-1">
                            {new Date(timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Log Summary */}
            {autonomousSync.logs.length > 0 && (
              <div className="mt-6 p-4 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Activity Summary</div>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>{autonomousSync.logs.filter(log => log.includes('✅') || log.includes('success')).length} Success</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>{autonomousSync.logs.filter(log => log.includes('❌') || log.includes('Error')).length} Errors</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                    <span>{autonomousSync.logs.filter(log => log.includes('⚠️') || log.includes('warn')).length} Warnings</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-blue-500" />
                    <span>{autonomousSync.logs.filter(log => log.includes('🔍') || log.includes('Discovery')).length} Discovery</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        {/* Database Sync Tab */}
        <TabsContent value="sync" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cloud className="h-5 w-5" />
                Database Synchronization Status
              </CardTitle>
              <CardDescription>
                Real-time sync status for all databases with CouchDB servers
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Sync Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Database className="h-4 w-4 text-blue-500" />
                    <span className="font-medium">Databases</span>
                  </div>
                  <div className="text-2xl font-bold">
                    {autonomousSync.robustSyncStatus ? autonomousSync.robustSyncStatus.filter((db: any) => db.isActive).length : 0}
                    <span className="text-sm text-muted-foreground ml-1">
                      / {autonomousSync.robustSyncStatus ? autonomousSync.robustSyncStatus.length : 4} active
                    </span>
                  </div>
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Server className="h-4 w-4 text-green-500" />
                    <span className="font-medium">Servers</span>
                  </div>
                  <div className="text-2xl font-bold">
                    {autonomousSync.serverInfo ? autonomousSync.serverInfo.filter((s: any) => s.isHealthy).length : 0}
                    <span className="text-sm text-muted-foreground ml-1">
                      / {autonomousSync.serverInfo ? autonomousSync.serverInfo.length : 0} healthy
                    </span>
                  </div>
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="h-4 w-4 text-purple-500" />
                    <span className="font-medium">Efficiency</span>
                  </div>
                  <div className="text-2xl font-bold">
                    {autonomousSync.healthSummary?.metrics?.syncEfficiency?.toFixed(1) || '0'}%
                  </div>
                </div>
              </div>

              {/* Database Status Cards */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Database Status</h3>
                {autonomousSync.robustSyncStatus && autonomousSync.robustSyncStatus.length > 0 ? (
                  <div className="grid gap-4">
                    {autonomousSync.robustSyncStatus.map((db: any) => (
                      <Card key={db.dbName} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              {getDatabaseIcon(db.dbName)}
                              <h4 className="font-semibold">{db.dbName}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${
                                db.isActive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
                              }`} />
                              <Badge variant={db.isActive ? 'default' : 'secondary'}>
                                {db.status}
                              </Badge>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Direction:</span>
                              <p className="font-medium">{db.direction}</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Docs Transferred:</span>
                              <p className="font-medium">{db.docsTransferred || 0}</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Conflicts:</span>
                              <p className="font-medium text-yellow-600">{db.conflicts || 0}</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Last Sync:</span>
                              <p className="font-medium">{formatTimestamp(db.lastSync)}</p>
                            </div>
                          </div>

                          {db.activeServer && (
                            <div className="mt-3 p-2 bg-muted rounded text-sm">
                              <div className="flex items-center gap-2">
                                <Link className="h-3 w-3" />
                                <span>Connected to: {db.activeServer.id}</span>
                                <Badge variant="outline" className="text-xs">
                                  {db.activeServer.responseTime}ms
                                </Badge>
                              </div>
                            </div>
                          )}

                          {db.error && (
                            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm">
                              <div className="flex items-center gap-2 text-red-700">
                                <AlertTriangle className="h-3 w-3" />
                                <span className="font-medium">Error:</span>
                              </div>
                              <p className="text-red-600 mt-1">{db.error}</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No database sync information available</p>
                    <p className="text-sm">Ensure CouchDB servers are discovered and sync is active</p>
                  </div>
                )}
              </div>

              {/* Server Status */}
              {autonomousSync.serverInfo && autonomousSync.serverInfo.length > 0 && (
                <div className="mt-8 space-y-4">
                  <h3 className="text-lg font-semibold">Server Status</h3>
                  <div className="grid gap-4">
                    {autonomousSync.serverInfo.map((server: any) => (
                      <Card key={server.id} className="border-l-4 border-l-green-500">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <Server className="h-4 w-4" />
                              <h4 className="font-semibold">{server.id}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${
                                server.isHealthy ? 'bg-green-500' : 'bg-red-500'
                              }`} />
                              <Badge variant={server.isHealthy ? 'default' : 'destructive'}>
                                {server.isHealthy ? 'Healthy' : 'Unhealthy'}
                              </Badge>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Response Time:</span>
                              <p className="font-medium">{server.responseTime}ms</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Error Count:</span>
                              <p className="font-medium">{server.errorCount}</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Priority:</span>
                              <p className="font-medium">{server.priority}</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Last Check:</span>
                              <p className="font-medium">{formatTimestamp(server.lastHealthCheck)}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sync Debug Tab */}
        <TabsContent value="sync-debug" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                PouchDB Sync Debug
              </CardTitle>
              <CardDescription>
                Debug information for PouchDB sync operations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Auth Data Status */}
              <div className="border rounded-lg p-4 bg-blue-50">
                <h3 className="font-semibold mb-3 flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Authentication Data Status
                </h3>
                <div className="space-y-2 text-sm">
                  {authDataStatus ? (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="font-medium">Status:</span>{' '}
                        <Badge variant={authDataStatus.exists && authDataStatus.valid ? 'default' : 'destructive'}>
                          {authDataStatus.exists ? (authDataStatus.valid ? 'Valid' : 'Invalid') : 'Missing'}
                        </Badge>
                      </div>
                      {authDataStatus.hasRestaurantId && (
                        <div>
                          <span className="font-medium">Restaurant ID:</span> {authDataStatus.restaurantId}
                        </div>
                      )}
                      {authDataStatus.keys && (
                        <div className="col-span-2">
                          <span className="font-medium">Available Keys:</span> {authDataStatus.keys.join(', ')}
                        </div>
                      )}
                      {authDataStatus.error && (
                        <div className="col-span-2">
                          <span className="font-medium text-red-600">Error:</span> {authDataStatus.error}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div>Loading auth data status...</div>
                  )}
                </div>
              </div>

              {/* Sync Bridge Debug Info */}
              <div className="border rounded-lg p-4 bg-green-50">
                <h3 className="font-semibold mb-3 flex items-center">
                  <Database className="mr-2 h-4 w-4" />
                  Sync Bridge Debug Information
                </h3>
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={loadSyncBridgeDebugInfo}
                      className="mb-2"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh Debug Info
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        localStorage.removeItem('pouchdb_sync_debug');
                        setSyncBridgeDebugInfo(null);
                      }}
                      className="mb-2"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Clear Debug Info
                    </Button>
                  </div>
                  {syncBridgeDebugInfo ? (
                    <div className="max-h-96 overflow-auto">
                      <pre className="text-xs bg-gray-100 p-3 rounded whitespace-pre-wrap">
                        {JSON.stringify(syncBridgeDebugInfo, null, 2)}
                      </pre>
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      No debug information available. Start a sync operation to see debug data.
                    </div>
                  )}
                </div>
              </div>

              {/* Force Database Initialization */}
              <div className="border rounded-lg p-4 bg-red-50">
                <h3 className="font-semibold mb-3 flex items-center">
                  <HardDrive className="mr-2 h-4 w-4" />
                  Force Database Initialization
                </h3>
                <div className="space-y-3">
                  <Button
                    size="sm"
                    onClick={async () => {
                      try {
                        const { PouchDBSyncBridge } = await import('@/lib/services/pouchdb-sync-bridge');
                        const syncBridge = new PouchDBSyncBridge({
                          autoSyncDatabases: [],
                          syncDirection: 'both',
                          retryAttempts: 1,
                          retryDelay: 1000,
                          batchSize: 50,
                          timeout: 10000
                        });
                        
                        const success = await syncBridge.forceDatabaseInitialization();
                        if (success) {
                          alert('✅ Database initialization successful!');
                        } else {
                          alert('❌ Database initialization failed. Check debug info.');
                        }
                        loadSyncBridgeDebugInfo();
                      } catch (error) {
                        alert(`❌ Database initialization error: ${error.message}`);
                      }
                    }}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Force Initialize Database
                  </Button>
                  <div className="text-xs text-muted-foreground">
                    Forces database initialization and setup. Use this if database is not ready.
                  </div>
                </div>
              </div>

              {/* Database Name Resolution Test */}
              <div className="border rounded-lg p-4 bg-yellow-50">
                <h3 className="font-semibold mb-3 flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Database Name Resolution Test
                </h3>
                <div className="space-y-3">
                  <Button
                    size="sm"
                    onClick={async () => {
                      try {
                        const { cleanRestaurantId, getRestaurantDbName } = await import('@/lib/db/db-utils');
                        const authData = localStorage.getItem('auth_data');
                        if (authData) {
                          const parsed = JSON.parse(authData);
                          if (parsed.restaurantId) {
                            const cleanedId = cleanRestaurantId(parsed.restaurantId);
                            const dbName = getRestaurantDbName(parsed.restaurantId);
                            alert(`Original: ${parsed.restaurantId}\nCleaned: ${cleanedId}\nDB Name: ${dbName}`);
                          } else {
                            alert('No restaurantId found in auth_data');
                          }
                        } else {
                          alert('No auth_data found');
                        }
                      } catch (error) {
                        alert(`Error: ${error.message}`);
                      }
                    }}
                  >
                    <TestTube className="h-4 w-4 mr-2" />
                    Test Database Name Resolution
                  </Button>
                  <div className="text-xs text-muted-foreground">
                    Tests the conversion from restaurant ID to database name
                  </div>
                </div>
              </div>

              {/* End-to-End Sync Test */}
              <div className="border rounded-lg p-4 bg-purple-50">
                <h3 className="font-semibold mb-3 flex items-center">
                  <Network className="mr-2 h-4 w-4" />
                  End-to-End Sync Test
                </h3>
                <div className="space-y-3">
                  <Button
                    size="sm"
                    onClick={async () => {
                      try {
                        const couchDBServers = autonomousSync.discoveredDevices.filter(device =>
                          device.serverInfo?.couchdb || (device.port >= 5984 && device.port <= 5987)
                        );

                        if (couchDBServers.length === 0) {
                          alert('No CouchDB servers found. Run discovery first.');
                          return;
                        }

                        const server = couchDBServers[0];
                        const serverUrl = `http://${server.ip}:${server.port}`;
                        
                        const { PouchDBSyncBridge } = await import('@/lib/services/pouchdb-sync-bridge');
                        const syncBridge = new PouchDBSyncBridge({
                          autoSyncDatabases: [],
                          syncDirection: 'both',
                          retryAttempts: 1,
                          retryDelay: 1000,
                          batchSize: 50,
                          timeout: 10000
                        });

                        const result = await syncBridge.testSyncFunctionality(serverUrl);
                        
                        if (result.success) {
                          alert(`✅ End-to-end sync test successful!\n\nDetails:\n${JSON.stringify(result.details, null, 2)}`);
                        } else {
                          alert(`❌ End-to-end sync test failed!\n\nError: ${result.message}`);
                        }
                        
                        loadSyncBridgeDebugInfo();
                      } catch (error) {
                        alert(`❌ Sync test error: ${error.message}`);
                      }
                    }}
                    disabled={autonomousSync.discoveredDevices.filter(device => 
                      device.serverInfo?.couchdb || (device.port >= 5984 && device.port <= 5987)
                    ).length === 0}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Test End-to-End Sync
                  </Button>
                  <div className="text-xs text-muted-foreground">
                    Full test: database init → connection → document sync → cleanup
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Diagnostics Tab */}
        <TabsContent value="diagnostics" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Sync Diagnostics
              </CardTitle>
              <CardDescription>
                Test database connectivity and sync functionality
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Quick Status Check */}
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <h4 className="font-medium mb-3">Quick Status Check</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center justify-between">
                        <span>CouchDB Servers Found:</span>
                        <Badge variant={autonomousSync.discoveredDevices.some(d => d.serverInfo?.couchdb || (d.port >= 5984 && d.port <= 5987)) ? 'default' : 'destructive'}>
                          {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb || (d.port >= 5984 && d.port <= 5987)).length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Active Sync Connections:</span>
                        <Badge variant={autonomousSync.robustSyncStatus?.some((db: any) => db.isActive) ? 'default' : 'secondary'}>
                          {autonomousSync.robustSyncStatus?.filter((db: any) => db.isActive).length || 0}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>PouchDB Ready:</span>
                        <Badge variant={isReady ? 'default' : 'destructive'}>
                          {isReady ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Network Status:</span>
                        <Badge variant={navigator.onLine ? 'default' : 'destructive'}>
                          {navigator.onLine ? 'Online' : 'Offline'}
                        </Badge>
                      </div>
                    </div>

                    {/* Common Issues */}
                    <div className="mt-4 space-y-2">
                      {!autonomousSync.discoveredDevices.some(d => d.serverInfo?.couchdb || (d.port >= 5984 && d.port <= 5987)) && (
                        <Alert>
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>No CouchDB servers found.</strong> Run network discovery or check if CouchDB is running.
                          </AlertDescription>
                        </Alert>
                      )}

                      {autonomousSync.discoveredDevices.some(d => d.serverInfo?.couchdb || (d.port >= 5984 && d.port <= 5987)) &&
                       (!autonomousSync.robustSyncStatus || !autonomousSync.robustSyncStatus.some((db: any) => db.isActive)) && (
                        <Alert>
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>Servers found but no active sync.</strong> Check CORS, authentication, or database existence.
                          </AlertDescription>
                        </Alert>
                      )}

                      {!isReady && (
                        <Alert>
                          <XCircle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>PouchDB not ready.</strong> Database initialization failed.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Mobile Sync Issue Diagnostics */}
                <Card className="bg-orange-50 border-orange-200">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-orange-800">
                      <Smartphone className="h-5 w-5" />
                      Mobile Sync Issue Diagnostics
                    </CardTitle>
                    <CardDescription className="text-orange-700">
                      Comprehensive debugging for mobile PouchDB sync issues
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Database Name Resolution */}
                      <div className="border rounded-lg p-4 bg-blue-50">
                        <h3 className="font-semibold mb-3 flex items-center">
                          <Database className="mr-2 h-4 w-4" />
                          Database Name Resolution Chain
                        </h3>
                        <div className="space-y-3 text-sm">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <strong>Auth Data:</strong>
                              <pre className="text-xs bg-white p-2 rounded mt-1 overflow-x-auto">
                                {(() => {
                                  try {
                                    const authData = localStorage.getItem('auth_data');
                                    if (!authData) return 'No auth_data found';
                                    const parsed = JSON.parse(authData);
                                    return JSON.stringify({
                                      restaurantId: parsed.restaurantId || 'MISSING',
                                      name: parsed.name || 'MISSING',
                                      userId: parsed.userId || 'MISSING'
                                    }, null, 2);
                                  } catch (e) {
                                    return `Error: ${e.message}`;
                                  }
                                })()}
                              </pre>
                            </div>
                            <div>
                              <strong>Expected DB Name:</strong>
                              <pre className="text-xs bg-white p-2 rounded mt-1 overflow-x-auto">
                                {(() => {
                                  try {
                                    const authData = localStorage.getItem('auth_data');
                                    if (!authData) return 'Cannot determine - no auth data';
                                    const parsed = JSON.parse(authData);
                                    if (!parsed.restaurantId) return 'Cannot determine - no restaurant ID';
                                    const cleanedId = parsed.restaurantId.replace(/[^a-z0-9]/gi, '_').toLowerCase();
                                    return `resto-${cleanedId}`;
                                  } catch (e) {
                                    return `Error: ${e.message}`;
                                  }
                                })()}
                              </pre>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* PouchDB Instance Status */}
                      <div className="border rounded-lg p-4 bg-green-50">
                        <h3 className="font-semibold mb-3 flex items-center">
                          <HardDrive className="mr-2 h-4 w-4" />
                          Local PouchDB Instance Status
                        </h3>
                        <div className="space-y-3 text-sm">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <strong>PouchDB Available:</strong>
                              <div className="mt-1">
                                {typeof window !== 'undefined' && (window as any).PouchDB ? '✅ Yes' : '❌ No'}
                              </div>
                            </div>
                            <div>
                              <strong>IndexedDB Available:</strong>
                              <div className="mt-1">
                                {typeof window !== 'undefined' && window.indexedDB ? '✅ Yes' : '❌ No'}
                              </div>
                            </div>
                            <div>
                              <strong>Capacitor Environment:</strong>
                              <div className="mt-1">
                                {typeof window !== 'undefined' && (window as any).Capacitor ? '✅ Yes' : '❌ No'}
                              </div>
                            </div>
                          </div>
                          <div>
                            <strong>PouchDB Capabilities:</strong>
                            <pre className="text-xs bg-white p-2 rounded mt-1 overflow-x-auto">
                              {(() => {
                                try {
                                  if (typeof window === 'undefined' || !(window as any).PouchDB) {
                                    return 'PouchDB not available';
                                  }
                                  const PouchDB = (window as any).PouchDB;
                                  const testDb = new PouchDB('test-capabilities-check');
                                  const capabilities = {
                                    hasReplicate: typeof testDb.replicate === 'object',
                                    hasReplicateTo: typeof testDb.replicate?.to === 'function',
                                    hasReplicateFrom: typeof testDb.replicate?.from === 'function',
                                    hasSync: typeof testDb.sync === 'function',
                                    hasPut: typeof testDb.put === 'function',
                                    hasGet: typeof testDb.get === 'function'
                                  };
                                  testDb.destroy().catch(() => {}); // Clean up
                                  return JSON.stringify(capabilities, null, 2);
                                } catch (e) {
                                  return `Error: ${e.message}`;
                                }
                              })()}
                            </pre>
                          </div>
                        </div>
                      </div>

                      {/* IndexedDB Database List */}
                      <div className="border rounded-lg p-4 bg-purple-50">
                        <h3 className="font-semibold mb-3 flex items-center">
                          <Database className="mr-2 h-4 w-4" />
                          Local IndexedDB Databases
                        </h3>
                        <div className="space-y-3 text-sm">
                          <Button
                            size="sm"
                            onClick={async () => {
                              if (typeof window !== 'undefined' && window.indexedDB && 'databases' in window.indexedDB) {
                                try {
                                  const databases = await (window.indexedDB as any).databases();
                                  console.log('IndexedDB databases:', databases);
                                  // Force re-render by updating a state
                                  setLastSyncTest(prev => prev ? {...prev} : null);
                                } catch (error) {
                                  console.error('Error listing databases:', error);
                                }
                              }
                            }}
                          >
                            List Local Databases
                          </Button>
                          <div className="text-xs text-muted-foreground">
                            Check browser console for database list
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Mobile Sync Connection Test */}
                <Card className="bg-yellow-50 border-yellow-200">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-yellow-800">
                      <Link className="h-5 w-5" />
                      Mobile Sync Connection Test
                    </CardTitle>
                    <CardDescription className="text-yellow-700">
                      Test actual sync functionality step by step
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <Button
                        onClick={async () => {
                          const couchDBServers = autonomousSync.discoveredDevices.filter(device =>
                            device.serverInfo?.couchdb || (device.port >= 5984 && device.port <= 5987)
                          );

                          if (couchDBServers.length === 0) {
                            setLastSyncTest({
                              status: 'no_servers',
                              message: 'No CouchDB servers found',
                              timestamp: new Date(),
                              server: '',
                              steps: []
                            });
                            return;
                          }

                          const server = couchDBServers[0];
                          const testResult: SyncTestResult = {
                            status: 'testing',
                            server: `${server.ip}:${server.port}`,
                            timestamp: new Date(),
                            steps: []
                          };
                          setLastSyncTest(testResult);

                          try {
                            // Step 1: Test basic connectivity
                            testResult.steps.push('Testing basic connectivity...');
                            setLastSyncTest({...testResult});

                            const response = await fetch(`http://${server.ip}:${server.port}/`, {
                              method: 'GET',
                              signal: AbortSignal.timeout(5000)
                            });

                            if (!response.ok) {
                              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }

                            testResult.steps.push('✅ Basic connectivity OK');
                            setLastSyncTest({...testResult});

                            // Step 2: Test database access
                            const authData = localStorage.getItem('auth_data');
                            if (!authData) {
                              throw new Error('No auth_data found');
                            }

                            const parsed = JSON.parse(authData);
                            if (!parsed.restaurantId) {
                              throw new Error('No restaurant ID in auth_data');
                            }

                            const cleanedId = parsed.restaurantId.replace(/[^a-z0-9]/gi, '_').toLowerCase();
                            const dbName = `resto-${cleanedId}`;

                            testResult.steps.push(`Testing database access: ${dbName}`);
                            setLastSyncTest({...testResult});

                            const dbResponse = await fetch(`http://${server.ip}:${server.port}/${dbName}`, {
                              method: 'GET',
                              headers: {
                                'Authorization': 'Basic ' + btoa('admin:admin'),
                                'Accept': 'application/json'
                              },
                              signal: AbortSignal.timeout(5000)
                            });

                            if (dbResponse.status === 404) {
                              testResult.steps.push(`❌ Database '${dbName}' not found on server`);
                              testResult.status = 'db_not_found';
                              testResult.dbName = dbName;
                            } else if (!dbResponse.ok) {
                              throw new Error(`Database access failed: HTTP ${dbResponse.status}`);
                            } else {
                              const dbInfo = await dbResponse.json();
                              testResult.steps.push(`✅ Database access OK - ${dbInfo.doc_count || 0} documents`);
                              testResult.dbInfo = dbInfo;

                              // Step 3: Test PouchDB sync setup
                              testResult.steps.push('Testing PouchDB sync setup...');
                              setLastSyncTest({...testResult});

                              if (typeof window !== 'undefined' && (window as any).PouchDB) {
                                const PouchDB = (window as any).PouchDB;
                                const localDb = new PouchDB(`test-sync-${Date.now()}`);
                                const remoteUrl = `http://admin:admin@${server.ip}:${server.port}/${dbName}`;

                                // Test document creation and sync
                                const testDoc = {
                                  _id: `sync-test-${Date.now()}`,
                                  type: 'sync_test',
                                  timestamp: new Date().toISOString(),
                                  deviceInfo: {
                                    userAgent: navigator.userAgent,
                                    isCapacitor: !!(window as any).Capacitor
                                  }
                                };

                                await localDb.put(testDoc);
                                testResult.steps.push('✅ Test document created locally');

                                // Try to replicate
                                const replication = localDb.replicate.to(remoteUrl, {
                                  live: false,
                                  retry: false,
                                  timeout: 10000
                                });

                                await new Promise((resolve, reject) => {
                                  replication.on('complete', (info: any) => {
                                    testResult.replicationInfo = info;
                                    resolve(info);
                                  });
                                  replication.on('error', reject);
                                  setTimeout(() => reject(new Error('Replication timeout')), 10000);
                                });

                                testResult.steps.push('✅ Replication test successful');
                                testResult.status = 'success';

                                // Clean up
                                await localDb.destroy();
                              } else {
                                testResult.steps.push('❌ PouchDB not available');
                                testResult.status = 'no_pouchdb';
                              }
                            }

                          } catch (error) {
                            testResult.steps.push(`❌ Error: ${error.message}`);
                            testResult.status = 'error';
                            testResult.error = error.message;
                          }

                          setLastSyncTest({...testResult});
                        }}
                        disabled={autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length === 0}
                        className="w-full"
                      >
                        <TestTube className="mr-2 h-4 w-4" />
                        Run Mobile Sync Connection Test
                      </Button>

                      {lastSyncTest && (
                        <div className="mt-4 p-4 border rounded-lg bg-white">
                          <h4 className="font-semibold mb-2">Sync Test Results</h4>
                          <div className="space-y-2 text-sm">
                            <div><strong>Status:</strong> <Badge variant={
                              lastSyncTest.status === 'success' ? 'default' :
                              lastSyncTest.status === 'db_not_found' ? 'destructive' :
                              lastSyncTest.status === 'error' ? 'destructive' : 'secondary'
                            }>{lastSyncTest.status}</Badge></div>
                            <div><strong>Server:</strong> {lastSyncTest.server}</div>
                            <div><strong>Time:</strong> {lastSyncTest.timestamp?.toLocaleTimeString()}</div>
                            {lastSyncTest.dbName && (
                              <div><strong>Database:</strong> {lastSyncTest.dbName}</div>
                            )}
                            {lastSyncTest.error && (
                              <div className="text-red-600"><strong>Error:</strong> {lastSyncTest.error}</div>
                            )}
                            {lastSyncTest.dbInfo && (
                              <div><strong>DB Info:</strong> {lastSyncTest.dbInfo.doc_count} docs, {lastSyncTest.dbInfo.update_seq} updates</div>
                            )}
                            {lastSyncTest.replicationInfo && (
                              <div><strong>Replication:</strong> {lastSyncTest.replicationInfo.docs_written} docs written</div>
                            )}
                            <div><strong>Steps:</strong></div>
                            <ul className="list-disc list-inside space-y-1 ml-4">
                              {lastSyncTest.steps?.map((step, i) => (
                                <li key={i}>{step}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Diagnostic Controls */}
                <div className="flex gap-4">
                  <Button
                    onClick={runSyncDiagnostics}
                    disabled={!autonomousSync.discoveredDevices.some(d => d.serverInfo?.couchdb || (d.port >= 5984 && d.port <= 5987))}
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Run General Diagnostics
                  </Button>

                  {syncDiagnostics && (
                    <Button
                      variant="outline"
                      onClick={() => setSyncDiagnostics(null)}
                    >
                      Clear Results
                    </Button>
                  )}
                </div>

                {/* Diagnostic Results */}
                {syncDiagnostics && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">Diagnostic Results</h3>
                      <Badge variant={syncDiagnostics.status === 'completed' ? 'default' : 'secondary'}>
                        {syncDiagnostics.status}
                      </Badge>
                    </div>

                    {syncDiagnostics.status === 'no_servers' && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          {syncDiagnostics.message}
                        </AlertDescription>
                      </Alert>
                    )}

                    {syncDiagnostics.servers && syncDiagnostics.servers.length > 0 && (
                      <div className="space-y-4">
                        {syncDiagnostics.servers.map((server: any) => (
                          <Card key={server.id} className="border-l-4 border-l-blue-500">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between mb-4">
                                <h4 className="font-semibold flex items-center gap-2">
                                  <Server className="h-4 w-4" />
                                  {server.id}
                                </h4>
                                <div className="flex items-center gap-2">
                                  <div className={`w-2 h-2 rounded-full ${
                                    server.reachable ? 'bg-green-500' : 'bg-red-500'
                                  }`} />
                                  <Badge variant={server.reachable ? 'default' : 'destructive'}>
                                    {server.reachable ? 'Reachable' : 'Unreachable'}
                                  </Badge>
                                </div>
                              </div>

                              {server.error && (
                                <Alert className="mb-4">
                                  <XCircle className="h-4 w-4" />
                                  <AlertDescription>
                                    {server.error}
                                  </AlertDescription>
                                </Alert>
                              )}

                              {server.reachable && (
                                <div className="space-y-3">
                                  <h5 className="font-medium">Database Tests</h5>
                                  <div className="grid gap-2">
                                    {Object.entries(server.databases).map(([dbName, result]: [string, any]) => (
                                      <div key={dbName} className="flex items-center justify-between p-2 bg-muted rounded">
                                        <div className="flex items-center gap-2">
                                          {getDatabaseIcon(dbName)}
                                          <span className="font-medium">{dbName}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          {result.success ? (
                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                          ) : (
                                            <XCircle className="h-4 w-4 text-red-500" />
                                          )}
                                          <Badge variant={result.success ? 'default' : 'destructive'}>
                                            {result.success ? 'OK' : 'Failed'}
                                          </Badge>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}

                    <div className="text-xs text-muted-foreground">
                      Diagnostic completed at: {formatTimestamp(syncDiagnostics.timestamp)}
                    </div>
                  </div>
                )}

                {/* Manual Connection Test */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Manual Connection Test</CardTitle>
                    <CardDescription>
                      Test specific server and database combinations
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Server IP</label>
                          <input
                            type="text"
                            value={customIP}
                            onChange={(e) => setCustomIP(e.target.value)}
                            placeholder="*************"
                            className="w-full mt-1 px-3 py-2 border rounded-md"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Port</label>
                          <input
                            type="text"
                            value={customPort}
                            onChange={(e) => setCustomPort(e.target.value)}
                            placeholder="5984"
                            className="w-full mt-1 px-3 py-2 border rounded-md"
                          />
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={async () => {
                            if (customIP && customPort) {
                              const serverUrl = `http://${customIP}:${customPort}`;
                              const result = await testSyncConnection(serverUrl, 'orders');
                              setLastSyncTest({
                                status: result.success ? 'success' : 'error',
                                server: serverUrl,
                                serverUrl,
                                dbName: 'orders',
                                result,
                                timestamp: new Date(),
                                steps: []
                              });
                            }
                          }}
                          disabled={!customIP || !customPort}
                        >
                          <Zap className="h-4 w-4 mr-2" />
                          Test Sync
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => autonomousSync.testSpecificIP(customIP, parseInt(customPort))}
                          disabled={!customIP || !customPort}
                        >
                          <Search className="h-4 w-4 mr-2" />
                          Test Discovery
                        </Button>
                      </div>

                      {lastSyncTest && (
                        <div className="mt-4 p-4 bg-muted rounded-lg">
                          <h5 className="font-medium mb-2">Last Test Result</h5>
                          <div className="text-sm space-y-1">
                            <p><strong>Server:</strong> {lastSyncTest.serverUrl}</p>
                            <p><strong>Database:</strong> {lastSyncTest.dbName}</p>
                            <p><strong>Time:</strong> {formatTimestamp(lastSyncTest.timestamp)}</p>
                            <div className={`mt-2 p-2 rounded ${
                              lastSyncTest.result.success ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                            }`}>
                              {lastSyncTest.result.success ? (
                                <div className="flex items-center gap-2">
                                  <CheckCircle className="h-4 w-4" />
                                  <span>{lastSyncTest.result.message}</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <XCircle className="h-4 w-4" />
                                  <span>{lastSyncTest.result.error}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Troubleshooter Tab */}
        <TabsContent value="troubleshooter" className="space-y-6 mt-6">
          <SyncTroubleshooter
            discoveredDevices={autonomousSync.discoveredDevices}
            className="w-full"
          />
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-time Monitoring
              </CardTitle>
              <CardDescription>
                Live sync metrics and health monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Health Summary */}
                {autonomousSync.healthSummary && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="border-l-4 border-l-green-500">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Activity className="h-4 w-4 text-green-500" />
                          <span className="font-medium">Overall Health</span>
                        </div>
                        <div className={`text-2xl font-bold ${
                          autonomousSync.healthSummary.overall === 'healthy' ? 'text-green-600' :
                          autonomousSync.healthSummary.overall === 'warning' ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {autonomousSync.healthSummary.overall.toUpperCase()}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <RotateCcw className="h-4 w-4 text-blue-500" />
                          <span className="font-medium">Active Syncs</span>
                        </div>
                        <div className="text-2xl font-bold text-blue-600">
                          {autonomousSync.healthSummary.metrics?.activeSyncs || 0}
                          <span className="text-sm text-muted-foreground ml-1">
                            / {autonomousSync.healthSummary.metrics?.totalDatabases || 0}
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-purple-500">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Timer className="h-4 w-4 text-purple-500" />
                          <span className="font-medium">Avg Response</span>
                        </div>
                        <div className="text-2xl font-bold text-purple-600">
                          {Math.round(autonomousSync.healthSummary.metrics?.averageResponseTime || 0)}ms
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Sync Efficiency */}
                {autonomousSync.healthSummary?.metrics && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Sync Efficiency</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium">Overall Efficiency</span>
                            <span className="text-sm text-muted-foreground">
                              {autonomousSync.healthSummary.metrics.syncEfficiency.toFixed(1)}%
                            </span>
                          </div>
                          <Progress value={autonomousSync.healthSummary.metrics.syncEfficiency} className="h-2" />
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Docs Transferred:</span>
                            <p className="font-medium">{autonomousSync.healthSummary.metrics.totalDocsTransferred}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Conflicts:</span>
                            <p className="font-medium text-yellow-600">{autonomousSync.healthSummary.metrics.totalConflicts}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Healthy Servers:</span>
                            <p className="font-medium">{autonomousSync.healthSummary.metrics.healthyServers}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Network Status:</span>
                            <Badge variant={autonomousSync.healthSummary.metrics.networkStatus === 'online' ? 'default' : 'destructive'}>
                              {autonomousSync.healthSummary.metrics.networkStatus}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Active Alerts */}
                {autonomousSync.healthSummary?.activeAlerts && autonomousSync.healthSummary.activeAlerts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        Active Alerts ({autonomousSync.healthSummary.activeAlerts.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-48">
                        <div className="space-y-2">
                          {autonomousSync.healthSummary.activeAlerts.map((alert: any) => (
                            <div key={alert.id} className={`p-3 rounded-lg border-l-4 ${
                              alert.type === 'error' ? 'border-l-red-500 bg-red-50' :
                              alert.type === 'warning' ? 'border-l-yellow-500 bg-yellow-50' :
                              'border-l-blue-500 bg-blue-50'
                            }`}>
                              <div className="flex items-start gap-2">
                                {alert.type === 'error' ? (
                                  <XCircle className="h-4 w-4 text-red-500 mt-0.5" />
                                ) : alert.type === 'warning' ? (
                                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5" />
                                ) : (
                                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5" />
                                )}
                                <div className="flex-1">
                                  <p className="font-medium text-sm">{alert.message}</p>
                                  <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                                    <span>{formatTimestamp(alert.timestamp)}</span>
                                    {alert.serverId && <span>Server: {alert.serverId}</span>}
                                    {alert.dbName && <span>DB: {alert.dbName}</span>}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                )}

                {/* Connection Health */}
                {autonomousSync.healthSummary?.connectionHealth && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Connection Health</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {autonomousSync.healthSummary.connectionHealth.map((conn: any) => (
                          <div key={conn.serverId} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${
                                conn.isHealthy ? 'bg-green-500' : 'bg-red-500'
                              }`} />
                              <span className="font-medium">{conn.serverId}</span>
                            </div>
                            <div className="flex items-center gap-4 text-sm">
                              <div>
                                <span className="text-muted-foreground">Response:</span>
                                <span className="ml-1 font-medium">{conn.responseTime}ms</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Uptime:</span>
                                <span className="ml-1 font-medium">{conn.uptime.toFixed(1)}%</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Throughput:</span>
                                <span className="ml-1 font-medium">{conn.throughput.toFixed(1)} docs/min</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Live Activity Log */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Live Activity Log
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <div className="space-y-1 font-mono text-xs">
                        {autonomousSync.logs && autonomousSync.logs.length > 0 ? (
                          autonomousSync.logs.slice(0, 50).map((log, index) => (
                            <div key={index} className="p-1 hover:bg-muted rounded">
                              {log}
                            </div>
                          ))
                        ) : (
                          <div className="text-center text-muted-foreground py-8">
                            No activity logs available
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}