'use client';

import React, { useState, useEffect } from 'react';

interface LogEntry {
  id: string;
  timestamp: Date;
  level: string;
  category: string;
  message: string;
  source: string;
}

export default function BasicP2PDebug() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [platform, setPlatform] = useState('unknown');

  useEffect(() => {
    // Detect platform
    if (typeof window !== 'undefined') {
      if ((window as any).electronAPI) {
        setPlatform('Desktop (Electron)');
      } else if ((window as any).Capacitor) {
        setPlatform('Mobile (Capacitor)');
      } else {
        setPlatform('Web Browser');
      }
    }

    // Add some test logs
    const testLogs: LogEntry[] = [
      {
        id: '1',
        timestamp: new Date(),
        level: 'info',
        category: 'system',
        message: 'P2P Debug interface initialized',
        source: 'BasicP2PDebug'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() + 1000),
        level: 'success',
        category: 'discovery',
        message: 'Network scanning started',
        source: 'NetworkScanner'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() + 2000),
        level: 'warn',
        category: 'network',
        message: 'No peers found on local network',
        source: 'HttpDiscovery'
      }
    ];

    setLogs(testLogs);

    // Simulate real-time logs
    const interval = setInterval(() => {
      const newLog: LogEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        level: Math.random() > 0.7 ? 'error' : Math.random() > 0.5 ? 'warn' : 'info',
        category: ['discovery', 'network', 'sync', 'mdns'][Math.floor(Math.random() * 4)],
        message: `Simulated log entry ${Date.now()}`,
        source: 'TestGenerator'
      };
      
      setLogs(prev => [newLog, ...prev].slice(0, 20)); // Keep only latest 20 logs
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warn': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'success': return '✅';
      default: return 'ℹ️';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">P2P Debug Console</h2>
        <p className="text-gray-600">
          Basic debugging interface for P2P discovery and sync operations
        </p>
      </div>

      {/* Platform Info */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
        <h3 className="text-lg font-semibold mb-2">Platform Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span className="text-sm text-gray-600">Platform:</span>
            <div className="font-mono text-sm">{platform}</div>
          </div>
          <div>
            <span className="text-sm text-gray-600">Total Logs:</span>
            <div className="font-mono text-sm">{logs.length}</div>
          </div>
          <div>
            <span className="text-sm text-gray-600">Status:</span>
            <div className="font-mono text-sm text-green-600">Active</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="mb-4 flex flex-wrap gap-2">
        <button
          onClick={() => setLogs([])}
          className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Clear Logs
        </button>
        <button
          onClick={() => {
            const newLog: LogEntry = {
              id: Date.now().toString(),
              timestamp: new Date(),
              level: 'info',
              category: 'system',
              message: 'Manual test log entry',
              source: 'UserAction'
            };
            setLogs(prev => [newLog, ...prev]);
          }}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Add Test Log
        </button>
        <button
          onClick={() => {
            const logsJson = JSON.stringify(logs, null, 2);
            const blob = new Blob([logsJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `p2p-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }}
          className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
        >
          Export Logs
        </button>
      </div>

      {/* Logs */}
      <div className="border rounded-lg">
        <div className="p-4 border-b bg-gray-50">
          <h3 className="text-lg font-semibold">Live Debug Logs ({logs.length})</h3>
          <p className="text-sm text-gray-600">Real-time P2P discovery and sync operation logs</p>
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <div className="text-4xl mb-4">📝</div>
              <p>No logs available</p>
              <p className="text-sm">Start discovery to see debug information</p>
            </div>
          ) : (
            <div className="divide-y">
              {logs.map((log) => (
                <div key={log.id} className={`p-4 border-l-4 ${getLevelColor(log.level)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <span className="text-lg">{getLevelIcon(log.level)}</span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="inline-block px-2 py-1 text-xs bg-gray-200 rounded">
                            {log.category}
                          </span>
                          <span className="inline-block px-2 py-1 text-xs bg-gray-100 rounded">
                            {log.source}
                          </span>
                          <span className="text-xs text-gray-500">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm font-medium break-words">{log.message}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">How to Use</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• This interface shows real-time P2P discovery and sync logs</li>
          <li>• Logs are automatically generated to demonstrate the system</li>
          <li>• Use "Add Test Log" to manually add entries</li>
          <li>• Export logs for sharing with developers</li>
          <li>• Access this page from the user menu on any screen</li>
        </ul>
      </div>
    </div>
  );
}
