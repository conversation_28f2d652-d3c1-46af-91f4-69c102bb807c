'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Wifi, 
  Server, 
  Smartphone, 
  Monitor,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  RefreshCw,
  Network,
  Database,
  Settings,
  Play,
  Square,
  Trash2,
  Download,
  Eye,
  EyeOff,
  AlertTriangle,
  Shield,
  HardDrive,
  Search,
  Link
} from 'lucide-react';
import { 
  p2pDebugLogger, 
  P2PLogEntry, 
  P2PDebugStats, 
  LogLevel, 
  LogCategory 
} from '@/lib/services/p2p-debug-logger';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';

interface P2PDebugInterfaceProps {
  className?: string;
}

export function P2PDebugInterface({ className }: P2PDebugInterfaceProps) {
  const [logs, setLogs] = useState<P2PLogEntry[]>([]);
  const [stats, setStats] = useState<P2PDebugStats | null>(null);
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<LogCategory | 'all'>('all');
  const [selectedLevel, setSelectedLevel] = useState<LogLevel | 'all'>('all');
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  // Get autonomous sync data for enhanced debugging
  const autonomousSync = useAutonomousSync(null, { enabled: false }); // Read-only for debugging

  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
  }, []);

  useEffect(() => {
    // Subscribe to logs
    const unsubscribeLogs = p2pDebugLogger.subscribe((newLogs) => {
      setLogs(newLogs);
    });

    // Subscribe to stats
    const unsubscribeStats = p2pDebugLogger.subscribeToStats((newStats) => {
      setStats(newStats);
    });

    return () => {
      unsubscribeLogs();
      unsubscribeStats();
    };
  }, []);

  const getFilteredLogs = () => {
    return logs.filter(log => {
      if (selectedCategory !== 'all' && log.category !== selectedCategory) return false;
      if (selectedLevel !== 'all' && log.level !== selectedLevel) return false;
      return true;
    });
  };

  const getLevelColor = (level: LogLevel) => {
    switch (level) {
      case 'error': return 'text-red-600 bg-red-50';
      case 'warn': return 'text-yellow-600 bg-yellow-50';
      case 'success': return 'text-green-600 bg-green-50';
      case 'info': return 'text-blue-600 bg-blue-50';
      case 'debug': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getLevelIcon = (level: LogLevel) => {
    switch (level) {
      case 'error': return <XCircle className="h-4 w-4" />;
      case 'warn': return <AlertCircle className="h-4 w-4" />;
      case 'success': return <CheckCircle className="h-4 w-4" />;
      case 'info': return <Activity className="h-4 w-4" />;
      case 'debug': return <Settings className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getCategoryIcon = (category: LogCategory) => {
    switch (category) {
      case 'discovery': return <Network className="h-4 w-4" />;
      case 'mdns': return <Wifi className="h-4 w-4" />;
      case 'http': return <Server className="h-4 w-4" />;
      case 'sync': return <RefreshCw className="h-4 w-4" />;
      case 'network': return <Activity className="h-4 w-4" />;
      case 'couchdb': return <Database className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const clearLogs = () => {
    p2pDebugLogger.clear();
  };

  const exportLogs = () => {
    const logsJson = p2pDebugLogger.exportLogs();
    const blob = new Blob([logsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `p2p-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderStatsCards = () => {
    if (!stats) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLogs}</div>
            <p className="text-xs text-muted-foreground">
              {stats.lastActivity ? `Last: ${stats.lastActivity.toLocaleTimeString()}` : 'No activity'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Errors</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.errorCount}</div>
            <p className="text-xs text-muted-foreground">
              {stats.warningCount} warnings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Platform</CardTitle>
            {isMobile ? <Smartphone className="h-4 w-4 text-blue-500" /> : <Monitor className="h-4 w-4 text-purple-500" />}
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{platform}</div>
            <p className="text-xs text-muted-foreground">
              {isMobile ? 'Mobile Client' : isDesktop ? 'Desktop Hub' : 'Web Browser'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Discovery</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{stats.categoryCounts.discovery}</div>
            <p className="text-xs text-muted-foreground">
              {stats.categoryCounts.http} HTTP, {stats.categoryCounts.mdns} mDNS
            </p>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderLogFilters = () => (
    <div className="flex flex-wrap gap-2 mb-4">
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium">Category:</span>
        <select 
          value={selectedCategory} 
          onChange={(e) => setSelectedCategory(e.target.value as LogCategory | 'all')}
          className="text-sm border rounded px-2 py-1"
        >
          <option value="all">All</option>
          <option value="discovery">Discovery</option>
          <option value="mdns">mDNS</option>
          <option value="http">HTTP</option>
          <option value="sync">Sync</option>
          <option value="network">Network</option>
          <option value="couchdb">CouchDB</option>
          <option value="system">System</option>
        </select>
      </div>

      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium">Level:</span>
        <select 
          value={selectedLevel} 
          onChange={(e) => setSelectedLevel(e.target.value as LogLevel | 'all')}
          className="text-sm border rounded px-2 py-1"
        >
          <option value="all">All</option>
          <option value="debug">Debug</option>
          <option value="info">Info</option>
          <option value="warn">Warning</option>
          <option value="error">Error</option>
          <option value="success">Success</option>
        </select>
      </div>

      <Button
        onClick={() => setIsAutoScroll(!isAutoScroll)}
        variant="outline"
        size="sm"
        className="ml-auto"
      >
        {isAutoScroll ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
        Auto-scroll
      </Button>

      <Button onClick={clearLogs} variant="outline" size="sm">
        <Trash2 className="h-4 w-4 mr-2" />
        Clear
      </Button>

      <Button onClick={exportLogs} variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
    </div>
  );

  const renderLogEntry = (log: P2PLogEntry) => (
    <div key={log.id} className={`p-3 border-l-4 mb-2 ${getLevelColor(log.level)} border-l-current`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2 flex-1">
          <div className="flex items-center space-x-1 mt-0.5">
            {getLevelIcon(log.level)}
            {getCategoryIcon(log.category)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <Badge variant="outline" className="text-xs">
                {log.category}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {log.source}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {log.timestamp.toLocaleTimeString()}
              </span>
            </div>
            <p className="text-sm font-medium break-words">{log.message}</p>
            {log.details && (
              <details className="mt-2">
                <summary className="text-xs text-muted-foreground cursor-pointer">
                  Show details
                </summary>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                  {JSON.stringify(log.details, null, 2)}
                </pre>
              </details>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const filteredLogs = getFilteredLogs();

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">P2P Debug Interface</h2>
        <p className="text-muted-foreground">
          Real-time debugging for LAN discovery, mDNS, HTTP scanning, and sync operations
        </p>
      </div>

      {renderStatsCards()}

      <Tabs defaultValue="logs" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="logs">Live Logs</TabsTrigger>
          <TabsTrigger value="sync">Sync Status</TabsTrigger>
          <TabsTrigger value="debug-detailed">Detailed Debug</TabsTrigger>
          <TabsTrigger value="network">Network Status</TabsTrigger>
          <TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                Live Debug Logs ({filteredLogs.length})
              </CardTitle>
              <CardDescription>
                Real-time P2P discovery and sync operation logs
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderLogFilters()}
              <ScrollArea className="h-96 w-full border rounded-lg p-4">
                {filteredLogs.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No logs available</p>
                    <p className="text-sm">Start discovery to see debug information</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredLogs.map(renderLogEntry)}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          {/* Sync Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                Sync Status Overview
              </CardTitle>
              <CardDescription>
                Real-time sync establishment and connection status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {autonomousSync.discoveredDevices.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Devices Found</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length}
                  </div>
                  <div className="text-sm text-muted-foreground">CouchDB Servers</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {autonomousSync.connectedPeers.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Active Syncs</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className={`text-2xl font-bold ${
                    autonomousSync.autonomousState.phase === 'connected' ? 'text-green-600' :
                    autonomousSync.autonomousState.phase === 'error' ? 'text-red-600' :
                    autonomousSync.autonomousState.phase === 'discovering' ? 'text-blue-600' : 'text-gray-600'
                  }`}>
                    {autonomousSync.autonomousState.phase === 'connected' ? '✅' :
                     autonomousSync.autonomousState.phase === 'error' ? '❌' :
                     autonomousSync.autonomousState.phase === 'discovering' ? '🔍' : '⏸️'}
                  </div>
                  <div className="text-sm text-muted-foreground capitalize">
                    {autonomousSync.autonomousState.phase}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <RefreshCw className="mr-2 h-5 w-5" />
                  Discovered Devices ({autonomousSync.discoveredDevices.length})
                </CardTitle>
                <CardDescription>
                  HTTP servers found via network discovery
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {autonomousSync.discoveredDevices.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      <Server className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No devices discovered yet</p>
                      <p className="text-xs">Discovery is {autonomousSync.isAutonomousActive ? 'running...' : 'stopped'}</p>
                    </div>
                  ) : (
                    autonomousSync.discoveredDevices.map((device) => (
                      <div key={`${device.ip}:${device.port}`} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          {device.serverInfo?.couchdb ? (
                            <Database className="h-4 w-4 text-green-500" />
                          ) : (
                            <Server className="h-4 w-4 text-blue-500" />
                          )}
                          <div>
                            <div className="font-medium">{device.ip}:{device.port}</div>
                            <div className="text-sm text-muted-foreground">
                              {device.hostname || 'Unknown hostname'}
                            </div>
                            {device.serverInfo?.couchdb && (
                              <div className="text-xs text-green-600">
                                ✅ CouchDB Ready for Sync
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={device.serverInfo?.couchdb ? "default" : "secondary"}>
                            {device.serverInfo?.couchdb ? 'CouchDB' : 'HTTP'}
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            {device.responseTime}ms
                          </div>
                          {device.isVerified && (
                            <div className="text-xs text-green-600 mt-1">
                              ✅ Verified
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="mr-2 h-5 w-5" />
                  Sync Connections ({autonomousSync.connectedPeers.length})
                </CardTitle>
                <CardDescription>
                  Active PouchDB ↔ CouchDB sync connections
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {autonomousSync.connectedPeers.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      <RefreshCw className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No sync connections established</p>
                      {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 ? (
                        <p className="text-xs text-yellow-600">
                          ⚠️ CouchDB servers found but sync not established
                        </p>
                      ) : (
                        <p className="text-xs">
                          Waiting for CouchDB servers to be discovered...
                        </p>
                      )}
                    </div>
                  ) : (
                    autonomousSync.connectedPeers.map((peer) => (
                      <div key={peer.id} className="flex items-center justify-between p-3 border rounded-lg bg-green-50">
                        <div className="flex items-center space-x-3">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <div>
                            <div className="font-medium">{peer.ip}:{peer.port}</div>
                            <div className="text-sm text-muted-foreground">
                              {peer.hostname || 'Connected peer'}
                            </div>
                            <div className="text-xs text-green-600">
                              🔄 PouchDB ↔ CouchDB Sync Active
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="default" className="bg-green-600">
                            Syncing
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            {peer.responseTime ? `${peer.responseTime}ms latency` : 'Connected'}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sync Progress and Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                Sync Process Status
              </CardTitle>
              <CardDescription>
                Step-by-step sync establishment progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Step 1: Discovery */}
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                    autonomousSync.discoveredDevices.length > 0 ? 'bg-green-500' :
                    autonomousSync.isAutonomousActive ? 'bg-blue-500' : 'bg-gray-400'
                  }`}>
                    1
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Network Discovery</div>
                    <div className="text-sm text-muted-foreground">
                      {autonomousSync.isAutonomousActive ? 'Scanning network for HTTP servers...' :
                       autonomousSync.discoveredDevices.length > 0 ? `Found ${autonomousSync.discoveredDevices.length} devices` :
                       'Waiting to start discovery'}
                    </div>
                  </div>
                  <div>
                    {autonomousSync.discoveredDevices.length > 0 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : autonomousSync.isAutonomousActive ? (
                      <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>

                {/* Step 2: CouchDB Detection */}
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                    autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 ? 'bg-green-500' :
                    autonomousSync.discoveredDevices.length > 0 ? 'bg-blue-500' : 'bg-gray-400'
                  }`}>
                    2
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">CouchDB Server Detection</div>
                    <div className="text-sm text-muted-foreground">
                      {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 ?
                        `Found ${autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length} CouchDB servers` :
                        autonomousSync.discoveredDevices.length > 0 ? 'Checking servers for CouchDB...' :
                        'Waiting for device discovery'}
                    </div>
                  </div>
                  <div>
                    {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : autonomousSync.discoveredDevices.length > 0 ? (
                      <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>

                {/* Step 3: Database Setup */}
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                    autonomousSync.isInitialized ? 'bg-green-500' : 'bg-gray-400'
                  }`}>
                    3
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Local Database Setup</div>
                    <div className="text-sm text-muted-foreground">
                      {autonomousSync.isInitialized ? 'PouchDB database ready' : 'Initializing local database...'}
                    </div>
                  </div>
                  <div>
                    {autonomousSync.isInitialized ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                    )}
                  </div>
                </div>

                {/* Step 4: Sync Establishment */}
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                    autonomousSync.connectedPeers.length > 0 ? 'bg-green-500' :
                    autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 && autonomousSync.isInitialized ? 'bg-blue-500' : 'bg-gray-400'
                  }`}>
                    4
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Sync Connection Establishment</div>
                    <div className="text-sm text-muted-foreground">
                      {autonomousSync.connectedPeers.length > 0 ?
                        `${autonomousSync.connectedPeers.length} sync connections active` :
                        autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 && autonomousSync.isInitialized ?
                        'Establishing PouchDB ↔ CouchDB sync...' :
                        'Waiting for CouchDB servers and database setup'}
                    </div>
                  </div>
                  <div>
                    {autonomousSync.connectedPeers.length > 0 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb).length > 0 && autonomousSync.isInitialized ? (
                      <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>

              {/* Current Status Message */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Current Status:</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">{autonomousSync.statusMessage}</p>
                <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                  <span>Phase: <span className="font-medium capitalize">{autonomousSync.autonomousState.phase}</span></span>
                  <span>Discovery: <span className="font-medium">{autonomousSync.isAutonomousActive ? 'Active' : 'Idle'}</span></span>
                  <span>Database: <span className="font-medium">{autonomousSync.isInitialized ? 'Ready' : 'Not Ready'}</span></span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Real-time Sync Activity Monitor */}
          {autonomousSync.connectedPeers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <RefreshCw className="mr-2 h-5 w-5" />
                  Live Sync Activity
                </CardTitle>
                <CardDescription>
                  Real-time sync events and document transfers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Sync Activity Indicators */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-3 border rounded-lg bg-blue-50">
                      <div className="text-lg font-bold text-blue-600">
                        <RefreshCw className="h-5 w-5 inline animate-spin mr-1" />
                        Active
                      </div>
                      <div className="text-xs text-muted-foreground">Sync Status</div>
                    </div>
                    <div className="text-center p-3 border rounded-lg bg-green-50">
                      <div className="text-lg font-bold text-green-600">
                        {autonomousSync.syncStatuses.reduce((total, status) => total + ((status as any).errorCount || 0), 0) === 0 ? '✅' : '⚠️'}
                        {autonomousSync.syncStatuses.reduce((total, status) => total + ((status as any).errorCount || 0), 0)} Errors
                      </div>
                      <div className="text-xs text-muted-foreground">Error Count</div>
                    </div>
                    <div className="text-center p-3 border rounded-lg bg-purple-50">
                      <div className="text-lg font-bold text-purple-600">
                        📊 {autonomousSync.syncStatuses.length}
                      </div>
                      <div className="text-xs text-muted-foreground">Active Syncs</div>
                    </div>
                  </div>

                  {/* Sync Connection Details */}
                  {autonomousSync.syncStatuses.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Sync Connection Details</h4>
                      {autonomousSync.syncStatuses.map((syncStatus, index) => (
                        <div key={(syncStatus as any).id || `sync-${index}`} className="p-3 border rounded-lg bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full ${
                                syncStatus.status === 'active' ? 'bg-green-500' :
                                syncStatus.status === 'error' ? 'bg-red-500' :
                                syncStatus.status === 'paused' ? 'bg-yellow-500' : 'bg-gray-400'
                              }`} />
                              <span className="font-medium text-sm">{syncStatus.peerId}</span>
                              <Badge variant="outline" className="text-xs">
                                {syncStatus.direction}
                              </Badge>
                            </div>
                            <div className="text-right">
                              <div className="text-xs text-muted-foreground">
                                {(syncStatus as any).lastSync ? `Last sync: ${new Date((syncStatus as any).lastSync).toLocaleTimeString()}` : 'Never synced'}
                              </div>
                              {((syncStatus as any).errorCount || 0) > 0 && (
                                <div className="text-xs text-red-600">
                                  {(syncStatus as any).errorCount} errors
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Quick Actions */}
                  <div className="flex space-x-2 pt-2 border-t">
                    <Button
                      onClick={() => autonomousSync.retryConnection()}
                      variant="outline"
                      size="sm"
                      disabled={!autonomousSync.isInitialized}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Retry Sync
                    </Button>
                    <Button
                      onClick={() => autonomousSync.clearCache?.()}
                      variant="outline"
                      size="sm"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Cache
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="debug-detailed" className="space-y-4">
          {/* Database Naming Debug Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="mr-2 h-5 w-5" />
                Database Naming & Identity Debug
              </CardTitle>
              <CardDescription>
                Detailed analysis of database naming consistency and self-sync prevention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Restaurant ID Processing */}
                <div className="border rounded-lg p-4 bg-blue-50">
                  <h3 className="font-semibold mb-3 flex items-center">
                    <HardDrive className="mr-2 h-4 w-4" />
                    Restaurant ID Processing Chain
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">1. Raw Restaurant ID:</span>
                        <div className="font-mono p-2 bg-white rounded border mt-1">
                          {(() => {
                            try {
                              const authData = localStorage.getItem('auth_data');
                              if (!authData) return '❌ No auth data';
                              const parsed = JSON.parse(authData);
                              return parsed.restaurantId || '❌ Missing';
                            } catch {
                              return '❌ Invalid auth data';
                            }
                          })()}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">2. Cleaned Restaurant ID:</span>
                        <div className="font-mono p-2 bg-white rounded border mt-1">
                          {(() => {
                            try {
                              const authData = localStorage.getItem('auth_data');
                              if (!authData) return '❌ No auth data';
                              const parsed = JSON.parse(authData);
                              if (!parsed.restaurantId) return '❌ Missing';
                              // Simulate cleanRestaurantId function
                              let cleanedId = parsed.restaurantId.replace(/^(restaurant[-_:])+/, '');
                              cleanedId = cleanedId.replace(/^(resto[-_:])+/, '');
                              cleanedId = cleanedId.replace(/[^a-zA-Z0-9_-]/g, '');
                              return cleanedId;
                            } catch {
                              return '❌ Error processing';
                            }
                          })()}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">3. Final Database Name:</span>
                        <div className="font-mono p-2 bg-white rounded border mt-1">
                          {(() => {
                            try {
                              const authData = localStorage.getItem('auth_data');
                              if (!authData) return '❌ No auth data';
                              const parsed = JSON.parse(authData);
                              if (!parsed.restaurantId) return '❌ Missing';
                              // Simulate full processing
                              let cleanedId = parsed.restaurantId.replace(/^(restaurant[-_:])+/, '');
                              cleanedId = cleanedId.replace(/^(resto[-_:])+/, '');
                              cleanedId = cleanedId.replace(/[^a-zA-Z0-9_-]/g, '');
                              return `resto-${cleanedId}`;
                            } catch {
                              return '❌ Error processing';
                            }
                          })()}
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-white rounded border">
                      <div className="text-sm">
                        <strong>Expected Format:</strong> <code>resto-2b28a072-d490-40e6-b447-0b56e5610249</code>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Both mobile and desktop must generate identical database names for sync to work
                      </div>
                    </div>
                  </div>
                </div>

                {/* Device Identity & Self-Sync Prevention */}
                <div className="border rounded-lg p-4 bg-yellow-50">
                  <h3 className="font-semibold mb-3 flex items-center">
                    <Shield className="mr-2 h-4 w-4" />
                    Device Identity & Self-Sync Prevention
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">My Device ID:</span>
                        <div className="font-mono p-2 bg-white rounded border mt-1">
                          {localStorage.getItem('device_id') || '❌ Not generated'}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Platform Type:</span>
                        <div className="font-mono p-2 bg-white rounded border mt-1">
                          {isMobile ? '📱 Mobile (Discovery Client)' : isDesktop ? '💻 Desktop (CouchDB Server)' : '🌐 Web Browser'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <span className="font-medium">Self-Sync Prevention Status:</span>
                      <div className="space-y-1">
                        {autonomousSync.discoveredDevices.map((device, index) => (
                          <div key={`${device.ip}:${device.port}`} className={`p-2 rounded border text-sm ${
                            (device as any).isSelf ? 'bg-red-100 border-red-300' : 'bg-green-100 border-green-300'
                          }`}>
                            <div className="flex items-center justify-between">
                              <span className="font-mono">{device.ip}:{device.port}</span>
                              <div className="flex items-center space-x-2">
                                {(device as any).isSelf ? (
                                  <Badge variant="destructive" className="text-xs">🚫 SELF - EXCLUDED</Badge>
                                ) : (
                                  <Badge variant="default" className="text-xs">✅ EXTERNAL - ALLOWED</Badge>
                                )}
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              Device ID: {(device as any).deviceId || 'Not available'} | 
                              Is Self: {(device as any).isSelf ? 'Yes' : 'No'} | 
                              CouchDB: {device.serverInfo?.couchdb ? 'Yes' : 'No'}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="p-3 bg-white rounded border">
                      <div className="text-sm">
                        <strong>Prevention Logic:</strong> Devices are excluded if they match my device ID, are localhost (127.0.0.1), or are flagged as self
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        This prevents desktop from trying to sync with its own CouchDB instance
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sync Verification Analysis */}
                <div className="border rounded-lg p-4 bg-green-50">
                  <h3 className="font-semibold mb-3 flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Sync Verification Analysis
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Connection Status:</span>
                        <div className="p-2 bg-white rounded border mt-1">
                          {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb && !(d as any).isSelf).length > 0 ? (
                            <div className="text-green-600">✅ CouchDB servers found and accessible</div>
                          ) : (
                            <div className="text-red-600">❌ No external CouchDB servers found</div>
                          )}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Sync Establishment:</span>
                        <div className="p-2 bg-white rounded border mt-1">
                          {autonomousSync.connectedPeers.length > 0 ? (
                            <div className="text-green-600">✅ {autonomousSync.connectedPeers.length} active sync connections</div>
                          ) : (
                            <div className="text-red-600">❌ No active sync connections</div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <span className="font-medium">Sync Connection Details:</span>
                      {autonomousSync.connectedPeers.length > 0 ? (
                        <div className="space-y-1">
                          {autonomousSync.connectedPeers.map((peer, index) => (
                            <div key={peer.id} className="p-2 bg-white rounded border text-sm">
                              <div className="flex items-center justify-between">
                                <span className="font-mono">{peer.ip}:{peer.port}</span>
                                <div className="flex items-center space-x-2">
                                  <Badge variant="default" className="text-xs">🔄 SYNCING</Badge>
                                  <span className="text-xs text-muted-foreground">{peer.responseTime}ms</span>
                                </div>
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                Last seen: {peer.lastSeen?.toLocaleTimeString() || 'Unknown'} | 
                                Verified: {peer.verified ? 'Yes' : 'No'}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="p-2 bg-white rounded border text-sm text-muted-foreground">
                          No active sync connections established
                        </div>
                      )}
                    </div>

                    <div className="p-3 bg-white rounded border">
                      <div className="text-sm">
                        <strong>Verification Process:</strong> 
                        <ol className="list-decimal list-inside mt-1 space-y-1 text-xs">
                          <li>Test remote CouchDB connectivity</li>
                          <li>Verify authentication (admin:admin)</li>
                          <li>Create test document in local PouchDB</li>
                          <li>Wait for sync to transfer document</li>
                          <li>Check if document exists on remote CouchDB</li>
                          <li>Clean up test document</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Problem Diagnosis */}
                <div className="border rounded-lg p-4 bg-red-50">
                  <h3 className="font-semibold mb-3 flex items-center">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    Problem Diagnosis
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="p-3 bg-white rounded border">
                        <div className="font-medium text-red-600">Database Naming Issues</div>
                        <ul className="text-xs mt-2 space-y-1">
                          <li>✓ Mobile and desktop use different database names</li>
                          <li>✓ Restaurant ID cleaning inconsistency</li>
                          <li>✓ Database name format validation missing</li>
                        </ul>
                      </div>
                      <div className="p-3 bg-white rounded border">
                        <div className="font-medium text-red-600">Self-Sync Prevention</div>
                        <ul className="text-xs mt-2 space-y-1">
                          <li>✓ Desktop tries to sync with own CouchDB</li>
                          <li>✓ No device ID comparison</li>
                          <li>✓ localhost detection missing</li>
                        </ul>
                      </div>
                      <div className="p-3 bg-white rounded border">
                        <div className="font-medium text-red-600">Sync Verification</div>
                        <ul className="text-xs mt-2 space-y-1">
                          <li>✓ Connection shows "established" but data doesn't sync</li>
                          <li>✓ No actual sync verification testing</li>
                          <li>✓ Silent sync failures</li>
                        </ul>
                      </div>
                    </div>

                    <div className="p-3 bg-white rounded border">
                      <div className="text-sm">
                        <strong>Current Status:</strong>
                        <div className="mt-2 space-y-1">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-xs">Database naming consistency: FIXED</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-xs">Self-sync prevention: FIXED</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-xs">Sync verification: FIXED</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span className="text-xs">Enhanced logging: ACTIVE</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Real-time Monitoring */}
                <div className="border rounded-lg p-4 bg-purple-50">
                  <h3 className="font-semibold mb-3 flex items-center">
                    <Activity className="mr-2 h-4 w-4" />
                    Real-time Monitoring
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div className="text-center p-3 bg-white rounded border">
                        <div className="text-lg font-bold text-blue-600">
                          {autonomousSync.discoveredDevices.length}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Devices</div>
                      </div>
                      <div className="text-center p-3 bg-white rounded border">
                        <div className="text-lg font-bold text-green-600">
                          {autonomousSync.discoveredDevices.filter(d => d.serverInfo?.couchdb && !(d as any).isSelf).length}
                        </div>
                        <div className="text-xs text-muted-foreground">Valid CouchDB</div>
                      </div>
                      <div className="text-center p-3 bg-white rounded border">
                        <div className="text-lg font-bold text-red-600">
                          {autonomousSync.discoveredDevices.filter(d => (d as any).isSelf).length}
                        </div>
                        <div className="text-xs text-muted-foreground">Self Excluded</div>
                      </div>
                      <div className="text-center p-3 bg-white rounded border">
                        <div className="text-lg font-bold text-purple-600">
                          {autonomousSync.connectedPeers.length}
                        </div>
                        <div className="text-xs text-muted-foreground">Active Syncs</div>
                      </div>
                    </div>

                    <div className="p-3 bg-white rounded border">
                      <div className="text-sm">
                        <strong>Current Phase:</strong> <span className="font-mono capitalize">{autonomousSync.autonomousState.phase}</span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {autonomousSync.statusMessage}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Network className="mr-2 h-5 w-5" />
                Network Status
              </CardTitle>
              <CardDescription>
                Current network configuration and connectivity status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Device Information</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Platform:</span>
                        <span className="font-mono">{platform}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Type:</span>
                        <span className="font-mono">
                          {isMobile ? 'Mobile Client' : isDesktop ? 'Desktop Hub' : 'Web Browser'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Role:</span>
                        <span className="font-mono">
                          {isMobile ? 'Discovery Client' : 'Service Publisher'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Port Configuration</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Next.js:</span>
                        <span className="font-mono">3000</span>
                      </div>
                      <div className="flex justify-between">
                        <span>CouchDB Primary:</span>
                        <span className="font-mono">5984</span>
                      </div>
                      <div className="flex justify-between">
                        <span>CouchDB Fallbacks:</span>
                        <span className="font-mono">5985-5987</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="diagnostics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Sync System Diagnostics
              </CardTitle>
              <CardDescription>
                Step-by-step troubleshooting for P2P sync issues
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Step 1: Authentication & Restaurant ID */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <Shield className="mr-2 h-4 w-4" />
                      1. Authentication & Restaurant ID
                    </h3>
                    <Badge variant={(() => {
                      try {
                        const authData = localStorage.getItem('auth_data');
                        if (!authData) return 'destructive';
                        const parsed = JSON.parse(authData);
                        return parsed.restaurantId ? 'default' : 'destructive';
                      } catch {
                        return 'destructive';
                      }
                    })()} >
                      {(() => {
                        try {
                          const authData = localStorage.getItem('auth_data');
                          if (!authData) return 'No Auth Data';
                          const parsed = JSON.parse(authData);
                          return parsed.restaurantId ? 'OK' : 'Missing ID';
                        } catch {
                          return 'Invalid JSON';
                        }
                      })()}
                    </Badge>
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span>Auth Data Present:</span>
                      <span className="font-mono">{localStorage.getItem('auth_data') ? '✅ Yes' : '❌ No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Restaurant ID:</span>
                      <span className="font-mono">{(() => {
                        try {
                          const authData = localStorage.getItem('auth_data');
                          if (!authData) return '❌ Missing';
                          const parsed = JSON.parse(authData);
                          return parsed.restaurantId ? `✅ ${parsed.restaurantId}` : '❌ Missing';
                        } catch {
                          return '❌ Invalid';
                        }
                      })()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Expected DB Name:</span>
                      <span className="font-mono">{(() => {
                        try {
                          const authData = localStorage.getItem('auth_data');
                          if (!authData) return '❌ N/A';
                          const parsed = JSON.parse(authData);
                          if (!parsed.restaurantId) return '❌ N/A';
                          const cleanId = parsed.restaurantId.toLowerCase().replace(/[^a-z0-9]/g, '');
                          return `✅ resto-${cleanId}`;
                        } catch {
                          return '❌ Error';
                        }
                      })()}</span>
                    </div>
                  </div>
                  {!localStorage.getItem('auth_data') && (
                    <Alert className="mt-3">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Fix:</strong> You need to log in first. Go to /auth to authenticate.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Step 2: Local Database Status */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <HardDrive className="mr-2 h-4 w-4" />
                      2. Local Database Status
                    </h3>
                    <Badge variant={(() => {
                      try {
                        return autonomousSync.isInitialized ? 'default' : 'destructive';
                      } catch {
                        return 'destructive';
                      }
                    })()} >
                      {autonomousSync.isInitialized ? 'Ready' : 'Not Ready'}
                    </Badge>
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span>PouchDB Initialized:</span>
                      <span className="font-mono">{autonomousSync.isInitialized ? '✅ Yes' : '❌ No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Database Instance:</span>
                      <span className="font-mono">{autonomousSync.isInitialized ? '✅ Available' : '❌ Missing'}</span>
                    </div>
                  </div>
                  {!autonomousSync.isInitialized && (
                    <Alert className="mt-3">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Fix:</strong> Database not initialized. Refresh the page or check authentication.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Step 3: Network Discovery */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <Search className="mr-2 h-4 w-4" />
                      3. Network Discovery
                    </h3>
                    <Badge variant={autonomousSync.discoveredDevices.length > 0 ? 'default' : 'secondary'}>
                      {autonomousSync.discoveredDevices.length} Found
                    </Badge>
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span>Discovery Active:</span>
                      <span className="font-mono">{autonomousSync.isAutonomousActive ? '✅ Running' : '❌ Stopped'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CouchDB Servers Found:</span>
                      <span className="font-mono">{autonomousSync.discoveredDevices.filter(d => d.port >= 5984 && d.port <= 5987).length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Verified Servers:</span>
                      <span className="font-mono">{autonomousSync.discoveredDevices.filter(d => d.isVerified).length}</span>
                    </div>
                  </div>
                  {autonomousSync.discoveredDevices.length === 0 && (
                    <Alert className="mt-3">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Fix:</strong> No CouchDB servers found. Ensure other devices are running and on the same network.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Step 4: CouchDB Server Connectivity */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <Server className="mr-2 h-4 w-4" />
                      4. CouchDB Server Connectivity
                    </h3>
                    <Badge variant={autonomousSync.discoveredDevices.some(d => d.isVerified) ? 'default' : 'destructive'}>
                      {autonomousSync.discoveredDevices.filter(d => d.isVerified).length} Reachable
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {autonomousSync.discoveredDevices.filter(d => d.port >= 5984 && d.port <= 5987).map(device => (
                      <div key={`${device.ip}:${device.port}`} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                        <span>{device.ip}:{device.port}</span>
                        <div className="flex items-center space-x-2">
                          {device.isVerified ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span className="font-mono">{device.responseTime}ms</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  {autonomousSync.discoveredDevices.length > 0 && !autonomousSync.discoveredDevices.some(d => d.isVerified) && (
                    <Alert className="mt-3">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Fix:</strong> CouchDB servers found but not reachable. Check if CouchDB is running with admin:admin credentials.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Step 5: Database Authentication */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <Shield className="mr-2 h-4 w-4" />
                      5. Database Authentication
                    </h3>
                    <Badge variant="secondary">
                      admin:admin
                    </Badge>
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span>Expected Credentials:</span>
                      <span className="font-mono">admin:admin</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Authentication Method:</span>
                      <span className="font-mono">Basic Auth</span>
                    </div>
                  </div>
                  <Alert className="mt-3">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Note:</strong> All CouchDB instances must be configured with admin:admin credentials for sync to work.
                    </AlertDescription>
                  </Alert>
                </div>

                {/* Step 6: Sync Connection Status */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <Link className="mr-2 h-4 w-4" />
                      6. Sync Connection Status
                    </h3>
                    <Badge variant={autonomousSync.connectedPeers.length > 0 ? 'default' : 'destructive'}>
                      {autonomousSync.connectedPeers.length} Active
                    </Badge>
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span>Sync Phase:</span>
                      <span className="font-mono">{autonomousSync.autonomousState.phase}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Connections:</span>
                      <span className="font-mono">{autonomousSync.connectedPeers.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sync Direction:</span>
                      <span className="font-mono">Bidirectional</span>
                    </div>
                  </div>
                  {autonomousSync.connectedPeers.length === 0 && autonomousSync.discoveredDevices.some(d => d.isVerified) && (
                    <Alert className="mt-3">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Fix:</strong> Servers found but sync not established. Check console logs for detailed error messages.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Step 7: Common Issues */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold flex items-center">
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      7. Common Issues & Solutions
                    </h3>
                  </div>
                  <div className="space-y-3 text-sm">
                    <div className="p-3 bg-red-50 rounded">
                      <h4 className="font-medium text-red-800">Sync never starts despite green discovery</h4>
                      <ul className="list-disc list-inside text-red-700 mt-1 space-y-1">
                        <li>Check if local database is initialized (Step 2)</li>
                        <li>Verify CouchDB authentication (admin:admin)</li>
                        <li>Ensure restaurant database exists on remote CouchDB</li>
                        <li>Check browser console for detailed error messages</li>
                      </ul>
                    </div>
                    <div className="p-3 bg-yellow-50 rounded">
                      <h4 className="font-medium text-yellow-800">Discovery finds devices but can't connect</h4>
                      <ul className="list-disc list-inside text-yellow-700 mt-1 space-y-1">
                        <li>Check if CouchDB is running on discovered IPs</li>
                        <li>Verify CouchDB admin interface is accessible (http://IP:5984/_utils)</li>
                        <li>Ensure CORS is enabled in CouchDB configuration</li>
                        <li>Check firewall settings on target devices</li>
                      </ul>
                    </div>
                    <div className="p-3 bg-blue-50 rounded">
                      <h4 className="font-medium text-blue-800">Authentication errors (401/403)</h4>
                      <ul className="list-disc list-inside text-blue-700 mt-1 space-y-1">
                        <li>CouchDB must have admin user: admin, password: admin</li>
                        <li>Check CouchDB configuration: [admins] admin = admin</li>
                        <li>Restart CouchDB after configuration changes</li>
                        <li>Verify authentication works: curl -u admin:admin http://IP:5984</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                Debug Configuration
              </CardTitle>
              <CardDescription>
                Configure debug logging and monitoring settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Maximum Log Entries</h4>
                    <p className="text-sm text-muted-foreground">
                      Number of log entries to keep in memory
                    </p>
                  </div>
                  <input
                    type="number"
                    min="100"
                    max="5000"
                    defaultValue="1000"
                    className="w-20 text-sm border rounded px-2 py-1"
                    onChange={(e) => p2pDebugLogger.setMaxLogs(parseInt(e.target.value) || 1000)}
                  />
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Quick Actions</h4>
                  <div className="flex space-x-2">
                    <Button 
                      onClick={() => {
                        p2pDebugLogger.info('system', 'Manual test log entry', 'P2PDebugInterface');
                      }}
                      variant="outline" 
                      size="sm"
                    >
                      Test Log
                    </Button>
                    <Button onClick={clearLogs} variant="outline" size="sm">
                      Clear All Logs
                    </Button>
                    <Button onClick={exportLogs} variant="outline" size="sm">
                      Export Logs
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default P2PDebugInterface;
