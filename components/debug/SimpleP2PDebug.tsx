'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, 
  Network,
  CheckCircle,
  XCircle,
  AlertCircle,
  Trash2,
  Download
} from 'lucide-react';
import { 
  p2pDebugLogger, 
  P2PLogEntry, 
  P2PDebugStats
} from '@/lib/services/p2p-debug-logger';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';

interface SimpleP2PDebugProps {
  className?: string;
}

export default function SimpleP2PDebug({ className }: SimpleP2PDebugProps) {
  const [logs, setLogs] = useState<P2PLogEntry[]>([]);
  const [stats, setStats] = useState<P2PDebugStats | null>(null);
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
  }, []);

  useEffect(() => {
    // Subscribe to logs
    const unsubscribeLogs = p2pDebugLogger.subscribe((newLogs) => {
      setLogs(newLogs.slice(0, 50)); // Show only latest 50 logs
    });

    // Subscribe to stats
    const unsubscribeStats = p2pDebugLogger.subscribeToStats((newStats) => {
      setStats(newStats);
    });

    return () => {
      unsubscribeLogs();
      unsubscribeStats();
    };
  }, []);

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warn': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  const clearLogs = () => {
    p2pDebugLogger.clear();
  };

  const exportLogs = () => {
    const logsJson = p2pDebugLogger.exportLogs();
    const blob = new Blob([logsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `p2p-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-xl md:text-2xl font-bold mb-2">P2P Debug Console</h2>
        <p className="text-muted-foreground text-sm md:text-base">
          Real-time debugging for LAN discovery and sync operations
        </p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalLogs}</div>
              <p className="text-xs text-muted-foreground">
                {stats.lastActivity ? `Last: ${stats.lastActivity.toLocaleTimeString()}` : 'No activity'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Errors</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.errorCount}</div>
              <p className="text-xs text-muted-foreground">
                {stats.warningCount} warnings
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Platform</CardTitle>
              <Network className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">{platform}</div>
              <p className="text-xs text-muted-foreground">
                {isMobile ? 'Mobile Client' : isDesktop ? 'Desktop Hub' : 'Web Browser'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Controls */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button onClick={clearLogs} variant="outline" size="sm">
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Logs
        </Button>
        <Button onClick={exportLogs} variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export Logs
        </Button>
        <Button 
          onClick={() => {
            p2pDebugLogger.info('system', 'Manual test log entry', 'SimpleP2PDebug');
          }}
          variant="outline" 
          size="sm"
        >
          Test Log
        </Button>
      </div>

      {/* Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            Live Debug Logs ({logs.length})
          </CardTitle>
          <CardDescription>
            Real-time P2P discovery and sync operation logs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-y-auto space-y-2">
            {logs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No logs available</p>
                <p className="text-sm">Start discovery to see debug information</p>
              </div>
            ) : (
              logs.map((log) => (
                <div key={log.id} className="p-3 border rounded-lg bg-muted/50">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-2 flex-1">
                      <div className="mt-0.5">
                        {getLevelIcon(log.level)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge variant="outline" className="text-xs">
                            {log.category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {log.source}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm font-medium break-words">{log.message}</p>
                        {log.details && (
                          <details className="mt-2">
                            <summary className="text-xs text-muted-foreground cursor-pointer">
                              Show details
                            </summary>
                            <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                              {JSON.stringify(log.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
